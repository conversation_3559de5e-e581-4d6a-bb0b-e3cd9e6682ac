import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup } from '@angular/forms';
import { InitialInputComponent } from '../initial-input/initial-input.component';
import { FormGridLayoutComponent } from '../form-grid-layout/form-grid-layout.component';

@Component({
  selector: 'app-form-container',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    InitialInputComponent,
    FormGridLayoutComponent
  ],
  templateUrl: './form-container.component.html',
  styleUrls: ['./form-container.component.scss']
})
export class FormContainerComponent {
  @Input() form!: FormGroup;
  @Input() columns: any[][] = [];
  @Input() columnCount: number = 1;
  @Input() isViewMode: boolean = false;
  @Input() isRowView: boolean = false;
  @Input() fields: any[] = [];
  @Input() idField: any = null;
  @Input() isIdFieldVisible: boolean = false;

  @Output() multiFieldAdded = new EventEmitter<{field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number}>();
  @Output() multiFieldRemoved = new EventEmitter<{fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number}>();
  @Output() groupAdded = new EventEmitter<{groupName: string, index: number}>();
  @Output() groupRemoved = new EventEmitter<{groupName: string, index: number}>();
  @Output() groupCloned = new EventEmitter<{groupName: string, index: number}>();

  onMultiFieldAdded(event: any): void {
    this.multiFieldAdded.emit(event);
  }

  onMultiFieldRemoved(event: any): void {
    this.multiFieldRemoved.emit(event);
  }

  onGroupAdded(event: any): void {
    this.groupAdded.emit(event);
  }

  onGroupRemoved(event: any): void {
    this.groupRemoved.emit(event);
  }

  onGroupCloned(event: any): void {
    this.groupCloned.emit(event);
  }
}