<div class="initial-input">
  <form [formGroup]="form" class="form-container">
    <div class="form-main-field">
      <label for="ID" class="form-label">ID</label>
      <div class="input-button-group">
        <div class="id-input-container">
          <input formControlName="ID" id="ID" type="text" class="form-input" [class]="getInputClass()" placeholder="Enter ID" required 
                 (input)="idInputChange.emit($event)" 
                 (focus)="idInputFocus.emit()" 
                 (blur)="idInputBlur.emit()" />
          
          <!-- Arrow button to toggle dropdown -->
          <button type="button" class="dropdown-arrow-btn" (click)="toggleIdDropdown.emit()" matTooltip="Show ID suggestions">
            <mat-icon>{{ showIdDropdown ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
          </button>
          
          <!-- Dropdown list for filtered results -->
          @if (showIdDropdown) {
            <div class="id-dropdown">
              @if (filteredIdOptions.length > 0) {
                @for (option of filteredIdOptions; track option.ID) {
                  <div class="id-dropdown-item" (click)="selectIdOption.emit(option)">
                    {{ option.ID }}
                  </div>
                }
              } @else {
                <div class="id-dropdown-empty">
                  No IDs found
                </div>
              }
            </div>
          }
        </div>

        <!-- زر الإضافة -->
        <button mat-raised-button color="primary" type="button" 
                (click)="loadDataAndBuildForm.emit()" 
                matTooltip="Add"
                class="initial-input-button add-button">
          <mat-icon>add</mat-icon>
        </button>

        <!-- زر التعديل -->
        <button mat-raised-button color="primary" type="button" 
                (click)="loadDataAndBuildForm.emit()" 
                matTooltip="Edit"
                class="initial-input-button edit-button">
          <mat-icon>edit</mat-icon>
        </button>

        <!-- زر العرض -->
        <button mat-raised-button color="accent" type="button" 
                (click)="viewData.emit()" 
                matTooltip="View"
                class="initial-input-button view-button">
          <mat-icon>visibility</mat-icon>
        </button>

        <!-- زر الصيانة -->
        <button mat-raised-button color="warn" type="button" 
                matTooltip="Maintenance"
                class="initial-input-button maintenance-button">
          <mat-icon>build</mat-icon>
        </button>
      </div>
    </div>
  </form>
</div> 