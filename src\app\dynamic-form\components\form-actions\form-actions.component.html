<div class="horizontal-container">
  <div class="form-field">
    <!-- <label>ID</label> -->
    <p>{{ idValue }}</p>
  </div>

  <div class="button-group">
    <!-- View toggle button -->
    <button mat-raised-button color="primary" type="button" (click)="toggleViewMode.emit()" matTooltip="Toggle View"
            class="form-action-button toggle-view-button">
      <mat-icon>{{ isRowView ? 'view_list' : 'view_module' }}</mat-icon>
      {{ isRowView ? 'Nested View' : 'Row View' }}
    </button>

    <!-- submit button -->
    @if (!isViewMode) {
      <button mat-raised-button color="primary" type="submit" [disabled]="isViewMode" matTooltip="Submit"
              class="form-action-button submit-button" (click)="onSubmit.emit()">
        <mat-icon>send</mat-icon>
      </button>
    }

    <!-- Validate button -->
    @if (!isViewMode) {
      <button mat-raised-button color="accent" type="button" (click)="validateRecord.emit()" matTooltip="Validate"
              class="form-action-button validate-button">
        <mat-icon>check_circle</mat-icon>
      </button>
    }

    <!-- Authorize button -->
    @if (!isViewMode) {
      <button mat-raised-button color="accent" type="button" (click)="authorizeRecord.emit()" matTooltip="Authorize"
              class="form-action-button authorize-button">
        <mat-icon>verified</mat-icon>
      </button>
    }

    <!-- back button -->
    <button mat-raised-button color="primary" type="button" (click)="goBack.emit()" matTooltip="Back"
            class="form-action-button back-button">
      <mat-icon>arrow_back</mat-icon>
    </button>

    <!-- Reject button -->
    @if (!isViewMode) {
      <button mat-raised-button color="warn" type="button" (click)="rejectRecord.emit()" matTooltip="Reject"
              class="form-action-button reject-button">
        <mat-icon>cancel</mat-icon>
      </button>
    }

    <!-- Delete button -->
    @if (!isViewMode) {
      <button mat-raised-button color="warn" type="button" (click)="deleteRecord.emit()" matTooltip="Delete"
              class="form-action-button delete-button">
        <mat-icon>delete</mat-icon>
      </button>
    }
    
    @if (errorMessage) {
      <div class="error-message">{{ errorMessage }}</div>
    }
  </div>
</div> 