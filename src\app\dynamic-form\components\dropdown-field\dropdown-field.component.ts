import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-dropdown-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule,
    HttpClientModule
  ],
  templateUrl: './dropdown-field.component.html',
  styleUrls: ['./dropdown-field.component.scss']
})
export class DropdownFieldComponent implements OnInit, OnDestroy {
  @Input() field: any;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() dropdownType: 'type' | 'foreignKey' | 'regular' = 'regular';
  @Input() fieldSuffix: string = '';

  @Output() optionSelected = new EventEmitter<{option: any, fieldName: string}>();

  // Internal state
  showDropdown: boolean = false;
  filteredOptions: any[] = [];
  searchTimeout: any;

  constructor(private http: HttpClient) {}

  ngOnInit(): void {
    // Initialize component
  }

  ngOnDestroy(): void {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  }

  getFieldName(): string {
    return this.fieldSuffix ? `${this.field.fieldName}_${this.fieldSuffix}` : this.field.fieldName;
  }

  getKeys(option: any): string[] {
    return Object.keys(option);
  }

  getPlaceholder(): string {
    return `Search ${this.field.label?.trim() || this.field.fieldName}`;
  }

  getTooltipText(): string {
    switch (this.dropdownType) {
      case 'type':
        return 'Show type suggestions';
      case 'foreignKey':
        return 'Show foreign key suggestions';
      default:
        return 'Show options';
    }
  }

  onInputChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;

    // Clear previous timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Set a new timeout to avoid too many API calls
    this.searchTimeout = setTimeout(() => {
      this.searchOptions(value);
    }, 300);
  }

  onInputFocus(): void {
    const currentValue = this.form.get(this.getFieldName())?.value || '';
    if (currentValue.trim() === '') {
      this.loadAllOptions();
    } else {
      this.searchOptions(currentValue);
    }
  }

  onInputBlur(): void {
    // Delay hiding dropdown to allow click on dropdown items
    setTimeout(() => {
      this.showDropdown = false;
    }, 200);
  }

  onToggleDropdown(): void {
    if (!this.showDropdown) {
      const currentValue = this.form.get(this.getFieldName())?.value || '';
      if (currentValue.trim() === '') {
        this.loadAllOptions();
      } else {
        this.searchOptions(currentValue);
      }
    } else {
      this.showDropdown = false;
    }
  }

  onSelectOption(option: any): void {
    const fieldName = this.getFieldName();
    const control = this.form.get(fieldName);
    if (control) {
      control.setValue(option.ROW_ID);
    }
    this.showDropdown = false;
    this.optionSelected.emit({ option, fieldName });
  }

  private searchOptions(searchTerm: string): void {
    if (searchTerm.trim() === '') {
      this.loadAllOptions();
      return;
    }
    this.loadAllAndFilter(this.getQueryBuilderId(), searchTerm);
  }

  private loadAllOptions(): void {
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${this.getQueryBuilderId()}`;
    const payload = {
      _select: ["ROW_ID"]
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.filteredOptions = response;
          this.showDropdown = true;
        } else {
          this.filteredOptions = [];
          this.showDropdown = true;
        }
      },
      error: () => {
        this.filteredOptions = [];
        this.showDropdown = true;
      }
    });
  }

  private loadAllAndFilter(queryBuilderId: string, searchTerm: string): void {
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const payload = {
      _select: ["ROW_ID"]
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          const filtered = response.filter(option =>
            option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase())
          );
          this.filteredOptions = filtered;
          this.showDropdown = true;
        } else {
          this.filteredOptions = [];
          this.showDropdown = true;
        }
      },
      error: () => {
        this.filteredOptions = [];
        this.showDropdown = true;
      }
    });
  }

  private getQueryBuilderId(): string {
    switch (this.dropdownType) {
      case 'type':
        return 'fieldType';
      case 'foreignKey':
        return 'formDefinition';
      default:
        return this.field.foreginKey || 'defaultQueryBuilder';
    }
  }
} 