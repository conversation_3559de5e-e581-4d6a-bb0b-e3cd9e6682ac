import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-dropdown-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './dropdown-field.component.html',
  styleUrls: ['./dropdown-field.component.scss']
})
export class DropdownFieldComponent {
  @Input() field: any;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() showDropdown: boolean = false;
  @Input() filteredOptions: any[] = [];
  @Input() dropdownType: 'type' | 'foreignKey' | 'regular' = 'regular';
  @Input() fieldSuffix: string = '';
  
  @Output() inputChange = new EventEmitter<Event>();
  @Output() inputFocus = new EventEmitter<void>();
  @Output() inputBlur = new EventEmitter<void>();
  @Output() toggleDropdown = new EventEmitter<void>();
  @Output() selectOption = new EventEmitter<any>();

  getFieldName(): string {
    return this.fieldSuffix ? `${this.field.fieldName}_${this.fieldSuffix}` : this.field.fieldName;
  }

  getKeys(option: any): string[] {
    return Object.keys(option);
  }

  getPlaceholder(): string {
    return `Search ${this.field.label?.trim() || this.field.fieldName}`;
  }

  getTooltipText(): string {
    switch (this.dropdownType) {
      case 'type':
        return 'Show type suggestions';
      case 'foreignKey':
        return 'Show foreign key suggestions';
      default:
        return 'Show options';
    }
  }

  onInputChange(event: Event): void {
    this.inputChange.emit(event);
  }

  onInputFocus(): void {
    this.inputFocus.emit();
  }

  onInputBlur(): void {
    this.inputBlur.emit();
  }

  onToggleDropdown(): void {
    this.toggleDropdown.emit();
  }

  onSelectOption(option: any): void {
    this.selectOption.emit(option);
  }
} 