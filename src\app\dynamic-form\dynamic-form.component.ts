import { Component, Input, OnInit, OnDestroy, Output, EventEmitter, ViewChild, ElementRef, AfterViewInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators, FormControl, AbstractControl } from "@angular/forms";
import { MetadataService } from '../services/metadata.service'
import { HttpClient } from "@angular/common/http";
import { CommonModule } from "@angular/common";
import { ReactiveFormsModule } from "@angular/forms";
import { environment } from '../../environments/environment';
import Choices from 'choices.js';

// Angular Material imports
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatAutocompleteModule } from '@angular/material/autocomplete';

// Custom components
import { SuccessPopupComponent } from './components/success-popup/success-popup.component';
import { InitialInputComponent } from './components/initial-input/initial-input.component';
import { ErrorMessageComponent } from './components/error-message/error-message.component';
import { FormActionsComponent } from './components/form-actions/form-actions.component';
import { FormGridLayoutComponent } from './components/form-grid-layout/form-grid-layout.component';
import { FormContainerComponent } from './components/form-container/form-container.component';
import { NestedGroupComponent } from './components/nested-group/nested-group.component';



@Component({
  selector: 'app-dynamic-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTooltipModule,
    MatExpansionModule,
    MatTableModule,
    MatChipsModule,
    MatAutocompleteModule,
    SuccessPopupComponent,
    InitialInputComponent,
    ErrorMessageComponent,
    FormActionsComponent,
    FormContainerComponent
  ],
  templateUrl: './dynamic-form.component.html',
  styleUrl: './dynamic-form.component.scss'
})
export class DynamicFormComponent implements OnInit, OnDestroy, AfterViewInit {
  columnCount = 1;
  columns: any[][] = [];
  @Input() tableName!: string;
  @Input() screenName!: string; // Add support for screen name
  @Input() data: any;
  @Output() dataChange = new EventEmitter<any>();
  @ViewChild('mySelect') mySelect!: ElementRef;

  form!: FormGroup;
  fields: any[] = [];
  submissionSuccess = false;
  errorMessage = "";
  isLoading = false;
  showInitialInput = true;
  isViewMode = false;
  successMessage: string = "";
  showSuccessPopup = false;
  showValidation: boolean = false;
  dropdownOptions: { [key: string]: any[] } = {};
  dropdownLoaded: { [key: string]: boolean } = {};
  dropdownVisible: { [key: string]: boolean } = {};
  validationResult: any;
  isTenantBasedFlag: boolean = false;
  authorizeNumber = 1;
  isRowView: boolean = false; // Toggle between nested view and row view

  // ID dropdown properties
  showIdDropdown = false;
  idOptions: any[] = [];
  filteredIdOptions: any[] = [];
  idSearchTimeout: any;

  // Dynamic dropdown properties
  showTypeDropdown: { [key: string]: boolean } = {};
  filteredTypeOptions: { [key: string]: any[] } = {};
  typeSearchTimeout: { [key: string]: any } = {};

  showForeignKeyDropdown: { [key: string]: boolean } = {};
  filteredForeignKeyOptions: { [key: string]: any[] } = {};
  foreignKeySearchTimeout: { [key: string]: any } = {};

  // Regular dropdown properties (for other foreign keys)
  showRegularDropdown: { [key: string]: boolean } = {};
  filteredRegularOptions: { [key: string]: any[] } = {};
  regularSearchTimeout: { [key: string]: any } = {};
  private metadataService = inject(MetadataService);
  private fb = inject(FormBuilder);
  private http = inject(HttpClient);
  constructor() { }

  ngOnInit() {
    if (this.tableName || this.screenName) {
      this.initializeForm();
    }
  }

  ngOnDestroy() {
    // Clear any pending timeouts
    if (this.idSearchTimeout) {
      clearTimeout(this.idSearchTimeout);
    }

    // Clear type search timeouts
    Object.values(this.typeSearchTimeout).forEach(timeout => {
      if (timeout) {
        clearTimeout(timeout);
      }
    });

    // Clear foreign key search timeouts
    Object.values(this.foreignKeySearchTimeout).forEach(timeout => {
      if (timeout) {
        clearTimeout(timeout);
      }
    });

    // Clear regular dropdown search timeouts
    Object.values(this.regularSearchTimeout).forEach(timeout => {
      if (timeout) {
        clearTimeout(timeout);
      }
    });
  }

  ngAfterViewInit() {
    // RESTORED FROM BACKUP - Simple dropdown initialization
    setTimeout(() => {
      this.fields.forEach((field) => {
        if (field.foreginKey) {
          const selectElement = document.getElementById(field.fieldName);
          if (selectElement) {
            new Choices(selectElement, {
              searchEnabled: true,
              itemSelectText: '',
            });
          }
        }
      });
    }, 200);
  }

  initializeForm() {
    this.form = this.fb.group({
      ID: ["", Validators.required],
    });
  }

  setFormReadonly(isReadonly: boolean) {
    const disableControls = (control: AbstractControl) => {
      if (control instanceof FormGroup) {
        Object.values(control.controls).forEach(disableControls);
      } else if (control instanceof FormArray) {
        control.controls.forEach(disableControls);
      } else {
        if (isReadonly) {
          control.disable({ emitEvent: false });
        } else {
          control.enable({ emitEvent: false });
        }
      }
    };

    disableControls(this.form); // Apply disable logic to the entire form

    // Explicitly handle isMulti fields
    this.fields.forEach((field) => {
      if (field.isMulti) {
        const formArray = this.form.get(field.fieldName) as FormArray;
        if (formArray) {
          formArray.controls.forEach((control) => {
            if (isReadonly) {
              control.disable({ emitEvent: false });
            } else {
              control.enable({ emitEvent: false });
            }
          });
        }
      }

      // Explicitly handle grouped fields
      if (field.Group) {
        const groupArray = this.getGroupArray(field.Group);
        if (groupArray) {
          groupArray.controls.forEach((control) => {
            if (isReadonly) {
              control.disable({ emitEvent: false });
            } else {
              control.enable({ emitEvent: false });
            }
          });
        }
      }
    });
  }

  viewData() {
    const idValue = this.form.get('ID')?.value;
    if (!idValue || idValue.trim() === '') {
      this.showValidation = true;
      return;
    }

    this.isViewMode = true;
    this.loadDataAndBuildForm();
    setTimeout(() => {
      this.setFormReadonly(true);
    }, 0);
  }

  loadDataAndBuildForm() {
    const idValue = this.form.get('ID')?.value;
    if (!idValue || idValue.trim() === '') {
      this.showValidation = true;
      return;
    }

    this.isLoading = true;
    this.errorMessage = "";
    this.successMessage = "";
    const id = this.form.get("ID")?.value;
    const tableNameForValidation = this.screenName ? this.screenName.split(',')[0] : this.tableName;
    const apiUrl = `${environment.baseURL}/api/validation/validate-id?tableName=${tableNameForValidation}&id=${id}`;

    this.http.get(apiUrl, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (response.success) {
          this.showInitialInput = false;
          this.loadTableMetadata();
        } else {
          this.errorMessage = response.message || "ID validation failed";
          this.isLoading = false;
        }
      },
      error: (error) => {
        this.errorMessage = "Error validating ID";
        this.isLoading = false;
      },
      complete: () => this.isLoading = false
    });
  }

  loadTableMetadata() {
    this.isLoading = true;
    const metadataObservable = this.screenName
      ? this.metadataService.getScreenMetadata(this.screenName)
      : this.metadataService.getTableMetadata(this.tableName);

    metadataObservable.subscribe({
      next: (response: any) => {
        if (response?.data?.fieldName) {

          // 🔍 DEBUG: Log actual API response structure
          console.log('🔍 ACTUAL API RESPONSE ANALYSIS:');
          console.log(`📊 Total fields in fieldName array: ${response.data.fieldName.length}`);

          // Track the exact 11 missing fields from your API response
          const missingFields = ['isTenantBased', 'isReadOnly', 'autoId', 'doNotAllowModificationAfterAuth', 'ID', 'recordStatus', 'tenantCode', 'recordInputter', 'recordAuthorizer', 'recordCount', 'dateTime'];
          console.log('🔍 TRACKING 11 MISSING FIELDS IN API RESPONSE:');
          missingFields.forEach(fieldName => {
            const fieldInArray = response.data.fieldName.find((f: any) => f.fieldName === fieldName);
            const valueAtRoot = response.data[fieldName];
            console.log(`📋 ${fieldName}: InFieldNameArray=${!!fieldInArray}, Type=${fieldInArray?.type}, Group=${fieldInArray?.Group}, RootValue=${valueAtRoot}`);
          });

          // 🔄 EXACT BACKUP RESTORATION: Simple field processing (lines 260-261)
          // ✅ Get columnNumber or default to 1
          this.columnCount = response.data.columnNumber || 1;

          // 🔄 EXACT BACKUP RESTORATION: Order fields as usual (backup line 264)
          const orderedFields = this.orderFieldsBasedOnFormDefinition(response.data.fieldName);

          // 🔍 DEBUG: Check fields after ordering
          console.log('🔍 AFTER ORDERING FIELDS:');
          console.log(`📊 Total ordered fields: ${orderedFields.length}`);
          missingFields.forEach(fieldName => {
            const fieldInOrdered = orderedFields.find((f: any) => f.fieldName === fieldName);
            console.log(`📋 ${fieldName}: InOrderedFields=${!!fieldInOrdered}, Type=${fieldInOrdered?.type}, Group=${fieldInOrdered?.Group}`);
          });

          // 🔍 Debug: Log field processing for verification
          console.log('🔍 FIELD PROCESSING DEBUG:');
          console.log(`📊 Total fields from API: ${response.data.fieldName.length}`);
          console.log(`📋 Fields with Group "fieldName": ${orderedFields.filter(f => f.Group === "fieldName").length}`);
          console.log(`📋 Fields with other Groups: ${orderedFields.filter(f => f.Group && f.Group !== "fieldName").length}`);
          console.log(`📋 Fields without Group: ${orderedFields.filter(f => !f.Group).length}`);
          console.log(`📋 Total ordered fields: ${orderedFields.length}`);

          // Log sample fields for each category
          console.log('📝 Sample fields with Group "fieldName":', orderedFields.filter(f => f.Group === "fieldName").slice(0, 3).map(f => f.fieldName));
          console.log('📝 Sample fields with other Groups:', orderedFields.filter(f => f.Group && f.Group !== "fieldName").slice(0, 3).map(f => `${f.fieldName} (${f.Group})`));
          console.log('📝 Sample fields without Group:', orderedFields.filter(f => !f.Group).slice(0, 3).map(f => f.fieldName));

          // 🔄 EXACT BACKUP RESTORATION: Skip "ID" so it doesn't take a layout slot (backup lines 266-268)
          const visibleFields = orderedFields.filter(
            field => field.fieldName?.toUpperCase() !== 'ID'
          );

          // 🔍 DEBUG: Check fields after filtering for visibility
          console.log('🔍 AFTER FILTERING FOR VISIBILITY:');
          console.log(`📊 Total visible fields: ${visibleFields.length}`);
          missingFields.forEach(fieldName => {
            const fieldInVisible = visibleFields.find((f: any) => f.fieldName === fieldName);
            console.log(`📋 ${fieldName}: InVisibleFields=${!!fieldInVisible}, Type=${fieldInVisible?.type}, Group=${fieldInVisible?.Group}`);
          });

          // 🔄 EXACT BACKUP RESTORATION: Store isTenantBased if present (backup lines 270-272)
          if (response.data.isTenantBased) {
            this.isTenantBasedFlag = response.data.isTenantBased;
          }

          // 🔄 EXACT BACKUP RESTORATION: Split fields into columns (backup line 275)
          this.columns = this.distributeFieldsRoundRobin(visibleFields, this.columnCount);

          // 🔍 DEBUG: Check fields after column distribution
          console.log('🔍 AFTER COLUMN DISTRIBUTION:');
          const allFieldsInColumns = this.columns.flat();
          console.log(`📊 Total fields in columns: ${allFieldsInColumns.length}`);
          missingFields.forEach(fieldName => {
            const fieldInColumns = allFieldsInColumns.find((f: any) => f.fieldName === fieldName);
            console.log(`📋 ${fieldName}: InColumns=${!!fieldInColumns}, Type=${fieldInColumns?.type}, Group=${fieldInColumns?.Group}`);
          });

          // 🔄 EXACT BACKUP RESTORATION: Still keep old field assignment (backup line 279)
          this.fields = orderedFields;

          this.buildForm();
          this.fetchFormData();
          // Handle defaultFields if present in metadata response
          if (response.data.defaultFields && Array.isArray(response.data.defaultFields)) {
            this.populateDefaultFields(response.data.defaultFields);
          }
        } else {
          this.isLoading = false;
        }
      },
      error: (error) => {
        this.isLoading = false;
      },
      complete: () => (this.isLoading = false),
    });
  }



  /**
   * Orders fields based on the formDefinition response structure
   * Fields with Group "fieldName" appear first, then other fields in their original order
   * RESTORED FROM BACKUP - Exact same logic for compatibility
   */
  private orderFieldsBasedOnFormDefinition(fields: any[]): any[] {
    if (!fields || !Array.isArray(fields)) {
      return fields;
    }

    // Separate fields with Group "fieldName" and preserve their order
    const fieldNameGroupFields = fields.filter(field => field.Group === "fieldName");

    // Get all other fields (non-fieldName group) and preserve their order
    // This includes fields with other Groups AND fields with no Group property
    const otherFields = fields.filter(field => field.Group !== "fieldName");

    // Combine: fieldName group fields first, then other fields in their original order
    return [
      ...fieldNameGroupFields,
      ...otherFields
    ];
  }

  /**
   * Simple round-robin field distribution (restored from backup)
   * This matches the original behavior exactly
   */
  private distributeFieldsRoundRobin(fields: any[], columnCount: number): any[][] {
    const columns: any[][] = Array.from({ length: columnCount }, () => []);
    fields.forEach((field, index) => {
      const colIndex = index % columnCount;
      columns[colIndex].push(field);
    });
    return columns;
  }

  // 🔄 EXACT BACKUP RESTORATION: buildForm method (backup lines 329-415)
  buildForm() {
    console.log('🔍 FORM BUILDING DEBUG:');
    console.log(`📊 Building form with ${this.fields.length} fields`);

    const groupedFields: { [key: string]: FormArray } = {};

    // First, create all parent groups
    const parentGroups = this.getParentGroups();
    parentGroups.forEach(parentGroup => {
      if (!groupedFields[parentGroup]) {
        groupedFields[parentGroup] = this.fb.array([]);
        this.form.addControl(parentGroup, groupedFields[parentGroup]);
        this.addGroup(parentGroup);
      }
    });

    // 🔄 EXACT BACKUP RESTORATION: Process field definition fields (backup lines 342-385)
    this.fields.forEach((field) => {
      if (field.fieldName !== "ID") {
        if (field.isMulti && !field.Group) {
          // Non-grouped multi-field
          const multiFieldArray = this.fb.array([this.createMultiField(field)]);
          this.form.addControl(field.fieldName, multiFieldArray);

          // Disable multi-field if noInput is true
          if (field.noInput) {
            multiFieldArray.disable({ emitEvent: false });
          }
        } else if (field.Group) {
          const parsed = this.parseGroupPath(field.Group);
          if (!parsed.isNested) {
            // Direct field of parent group - already handled in createGroup
          } else {
            // Nested group field - already handled in createGroup
          }
        } else {
          // Non-grouped regular field
          const validators = field.mandatory ? Validators.required : null;
          let control;
          switch (field.type) {
            case "boolean":
              control = this.fb.control(false, validators);
              break;
            case "date":
              control = this.fb.control(null, validators);
              break;
            default:
              control = this.fb.control("", validators);
              break;
          }
          this.form.addControl(field.fieldName, control);

          // Disable control if noInput is true
          if (field.noInput) {
            control.disable({ emitEvent: false });
          }

          if (field.foreginKey) {
            this.dropdownLoaded[field.fieldName] = false;
          }
        }
      }
    });

    // 🔍 DEBUG: Check form controls for missing fields
    console.log('🔍 FORM CONTROLS CREATED:');
    const missingFields = ['isTenantBased', 'isReadOnly', 'autoId', 'doNotAllowModificationAfterAuth', 'recordStatus', 'tenantCode', 'recordInputter', 'recordAuthorizer', 'recordCount', 'dateTime'];
    missingFields.forEach(fieldName => {
      const hasControl = this.form.get(fieldName) !== null;
      console.log(`🎛️ ${fieldName}: Form control exists=${hasControl}`);
    });
    console.log(`🎛️ Total form controls: ${Object.keys(this.form.controls).length}`);
  }




  loadDropdownOptions(fieldName: string, foreignKey: string, groupIndex?: number, multiIndex?: number, groupName?: string) {
    if (this.dropdownLoaded[fieldName]) {
      return;
    }

    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${foreignKey}`;
    const payload = {
      _select: ["ROW_ID"]
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response) => {
        if (Array.isArray(response)) {
          this.dropdownOptions[fieldName] = response;
          this.dropdownLoaded[fieldName] = true;

          if (groupIndex !== undefined && multiIndex !== undefined && groupName) {
            const multiArray = this.getMultiArray(fieldName, groupIndex, groupName);
            const control = multiArray.at(multiIndex) as FormGroup;
            control.get(fieldName)?.setValue(response[0]?.ROW_ID || "");
          }
        } else {
          // Handle unexpected response
        }
      },
      error: (error) => {
        // Handle error silently
      }
    });


  }

  createGroup(groupName: string): FormGroup {
    const group = this.fb.group({});

    // Handle nested groups
    const parsed = this.parseGroupPath(groupName);
    if (parsed.isNested && parsed.parent && parsed.child) {
      // This is a nested group, create fields for this specific path
      this.getFieldsForGroupPath(groupName).forEach((field) => {
        this.addFieldToGroup(group, field);
      });
    } else {
      // This is a parent group, create fields and nested subgroups
      this.getFieldsForGroup(groupName).forEach((field) => {
        const fieldParsed = this.parseGroupPath(field.Group);
        if (!fieldParsed.isNested) {
          // Direct field of this group
          this.addFieldToGroup(group, field);
        }
      });

      // Add nested subgroups
      const childGroups = this.getChildGroups(groupName);
      childGroups.forEach(childGroup => {
        const childGroupArray = this.fb.array([this.createGroup(`${groupName}|${childGroup}`)]);
        group.addControl(childGroup, childGroupArray);
      });
    }

    return group;
  }

  createMultiField(field: any): FormGroup {
    const group = this.fb.group({});
    if (Array.isArray(field)) {
      field.forEach(fieldName => {
        const control = this.fb.control('', Validators.required);
        group.addControl(fieldName, control);
      });
    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
    return group;
  }

  /**
   * Helper method to add a field to a FormGroup
   */
  private addFieldToGroup(group: FormGroup, field: any): void {
    if (field.isMulti) {
      const multiFieldArray = this.fb.array([this.createMultiField(field)]);
      group.addControl(field.fieldName, multiFieldArray);

      // Disable multi-field if noInput is true
      if (field.noInput) {
        multiFieldArray.disable({ emitEvent: false });
      }
    } else if (field.foreginKey) {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }

      this.dropdownLoaded[field.fieldName] = false;
    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
  }



  getFieldsForGroup(groupName: string) {
    return this.fields.filter((field) => field.Group && field.Group.trim() === groupName.trim());
  }

  /**
   * Get fields for a specific group path (supports nested groups with pipe notation)
   * @param groupPath - The full group path (e.g., "type|field")
   * @returns Array of fields belonging to this group path
   */
  getFieldsForGroupPath(groupPath: string) {
    return NestedGroupComponent.getFieldsForNestedGroupPath(this.fields, groupPath);
  }

  /**
   * Parse group path to get parent and child group names
   * @param groupPath - The group path (e.g., "type|field")
   * @returns Object with parent and child group names
   */
  parseGroupPath(groupPath: string): { parent: string | null, child: string | null, isNested: boolean } {
    return NestedGroupComponent.parseNestedGroupPath(groupPath);
  }

  /**
   * Get all unique parent groups
   */
  getParentGroups(): string[] {
    const parentGroups = new Set<string>();
    this.fields.forEach(field => {
      if (field.Group) {
        const parsed = this.parseGroupPath(field.Group);
        if (parsed.parent) {
          parentGroups.add(parsed.parent);
        }
      }
    });
    return Array.from(parentGroups);
  }

  /**
   * Get all child groups for a specific parent group
   */
  getChildGroups(parentGroup: string): string[] {
    return NestedGroupComponent.getChildGroups(this.fields, parentGroup);
  }

  getGroupArray(groupName: string): FormArray {
    return this.form.get(groupName) as FormArray;
  }

  /**
   * Get nested group array using path notation
   * @param groupPath - Path like "type|field" for nested groups
   * @param parentIndex - Index of parent group instance
   */
  getNestedGroupArray(groupPath: string, parentIndex?: number): FormArray {
    const parsed = this.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child && parentIndex !== undefined) {
      const parentArray = this.getGroupArray(parsed.parent);
      const parentGroup = parentArray.at(parentIndex) as FormGroup;
      return parentGroup.get(parsed.child) as FormArray;
    }
    return this.getGroupArray(groupPath);
  }

  addGroup(groupName: string, index?: number) {
    const groupArray = this.getGroupArray(groupName);
    const newGroup = this.createGroup(groupName);

    if (index !== undefined) {
      groupArray.insert(index + 1, newGroup);
    } else {
      groupArray.push(newGroup);
    }
  }

  /**
   * Add nested group
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index to insert at (optional)
   */
  addNestedGroup(groupPath: string, parentIndex: number, index?: number) {
    const parsed = this.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child) {
      const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
      const newGroup = this.createGroup(groupPath);

      if (index !== undefined) {
        nestedArray.insert(index + 1, newGroup);
      } else {
        nestedArray.push(newGroup);
      }
    }
  }

  removeGroup(groupName: string, index: number) {
    this.getGroupArray(groupName).removeAt(index);
  }

  /**
   * Remove nested group
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index of nested group to remove
   */
  removeNestedGroup(groupPath: string, parentIndex: number, index: number) {
    const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
    nestedArray.removeAt(index);
  }

  /**
   * Clone nested group
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index of nested group to clone
   */
  cloneNestedGroup(groupPath: string, parentIndex: number, index: number) {
    const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
    const groupToClone = nestedArray.at(index) as FormGroup;
    const clonedGroup = this.createGroup(groupPath);

    // Copy values from the original group to the cloned group
    Object.keys(groupToClone.controls).forEach(key => {
      const originalControl = groupToClone.get(key);
      const clonedControl = clonedGroup.get(key);

      if (originalControl && clonedControl) {
        if (originalControl instanceof FormArray) {
          // Handle FormArray cloning
          const originalArray = originalControl as FormArray;
          const clonedArray = clonedControl as FormArray;

          // Clear the default entry and copy all entries from original
          clonedArray.clear();
          originalArray.controls.forEach(control => {
            if (control instanceof FormGroup) {
              const newControl = this.fb.group({});
              Object.keys(control.controls).forEach(subKey => {
                newControl.addControl(subKey, this.fb.control(control.get(subKey)?.value));
              });
              clonedArray.push(newControl);
            }
          });
        } else {
          // Handle regular FormControl cloning
          clonedControl.setValue(originalControl.value);
        }
      }
    });

    nestedArray.insert(index + 1, clonedGroup);
  }

  getMultiArray(fieldName: string, groupIndex?: number, groupName?: string, nestedGroupIndex?: number): FormArray {
    if (groupIndex !== undefined && groupName) {
      // Check if this is a nested group
      const parsed = this.parseGroupPath(groupName);
      if (parsed.isNested && parsed.parent && parsed.child && nestedGroupIndex !== undefined) {
        // Navigate to nested group: parent[groupIndex].child[nestedGroupIndex].fieldName
        const parentArray = this.getGroupArray(parsed.parent);
        const parentGroup = parentArray.at(groupIndex) as FormGroup;
        const childArray = parentGroup.get(parsed.child) as FormArray;
        const childGroup = childArray.at(nestedGroupIndex) as FormGroup;
        return childGroup.get(fieldName) as FormArray;
      } else {
        // Regular group
        const groupArray = this.getGroupArray(groupName);
        const group = groupArray.at(groupIndex) as FormGroup;
        return group.get(fieldName) as FormArray;
      }
    } else {
      return this.form.get(fieldName) as FormArray;
    }
  }



  isFirstFieldInGroup(field: any): boolean {
    return (
      this.fields.findIndex((f) => f.Group === field.Group) ===
      this.fields.indexOf(field)
    );
  }

  /**
   * Check if this is the first field in a parent group (for rendering group headers)
   */
  isFirstFieldInParentGroup(field: any): boolean {
    if (!field.Group) return false;

    const parsed = this.parseGroupPath(field.Group);
    if (!parsed.parent) return false;

    // For simple groups (not nested), just check if this is the first field with this group
    if (!parsed.isNested) {
      return this.fields.findIndex((f) => f.Group === field.Group) === this.fields.indexOf(field);
    }

    // For nested groups, find the first field that belongs to this parent group
    const firstFieldIndex = this.fields.findIndex((f) => {
      if (!f.Group) return false;
      const fParsed = this.parseGroupPath(f.Group);
      return fParsed.parent === parsed.parent;
    });

    return firstFieldIndex === this.fields.indexOf(field);
  }

  /**
   * Check if this is the first field in a nested group (for rendering nested group headers)
   */
  isFirstFieldInNestedGroup(field: any): boolean {
    return NestedGroupComponent.isFirstFieldInNestedGroup(field, this.fields);
  }

  trackByFieldName(_index: number, field: any): string {
    return field.fieldName;
  }

  authorizeRecord() {
    this.errorMessage = "";
    this.isLoading = true;

    const id = this.form.get("ID")?.value;
    const tablesApiId = this.extractTablesApiId(this.tableName);
    const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}/authorize`;
    const params = {
      isTenantBased: this.isTenantBasedFlag.toString(),
      authorizeNumber: this.authorizeNumber
    };
    this.http.put(apiUrl, {}, { withCredentials: true, params }).subscribe({
      next: (response: any) => {
        if (response && response.status === "success") {
          this.showSuccessPopup = true;
          this.successMessage = "Record authorized successfully!";
          this.isViewMode = false;
          this.setFormReadonly(false);
          this.goBack();
          setTimeout(() => {
            this.showSuccessPopup = false;
          }, 20000);
        } else {
          this.errorMessage = response.message || "Authorization failed";
        }
      },
      error: (error) => {
        this.errorMessage = "An error occurred during authorization";
      },
      complete: () => this.isLoading = false
    });
  }

  onSubmit() {
    this.errorMessage = "";

    if (this.form.valid) {
      const tablesApiId = this.extractTablesApiId(this.tableName);
      const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records`;
      const formData = this.buildFormData(this.form.getRawValue());
      const params = {
        isTenantBased: this.isTenantBasedFlag.toString(),
        authorizeNumber: this.authorizeNumber
      };
      this.http.post(apiUrl, formData, { withCredentials: true, params }).subscribe({
        next: (response: any) => {
          if (response.status === "success") {
            this.showSuccessPopup = true;
            this.successMessage = "Record submitted successfully!";
            this.goBack();
            setTimeout(() => {
              this.showSuccessPopup = false;
            }, 20000);
          } else if (response.status === "error") {
            this.errorMessage = response.message[0].error || "An error occurred while submitting the form";
          }
        },
        error: (error: any) => {
          if (error.error && error.error.message) {
            this.errorMessage = error.error.message[0].error;
          } else {
            this.errorMessage = "An unexpected error occurred while submitting the form";
          }
        }
      });
    }
  }

  validateRecord() {
    this.errorMessage = "";
    this.isLoading = true;

    if (this.form.valid) {
      const tablesApiId = this.extractTablesApiId(this.tableName);
      const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/validate`;
      const formData = this.buildFormData(this.form.getRawValue());
      const params = {
        isTenantBased: this.isTenantBasedFlag.toString(),
        authorizeNumber: this.authorizeNumber
      };
      this.http.post(apiUrl, formData, { withCredentials: true, params }).subscribe({
        next: (response: any) => {
          if (response.status === "success") {
            this.validationResult = response.data;
            this.populateForm(response.data);
            // Handle defaultFields even in error response
            if (response.defaultFields && Array.isArray(response.defaultFields)) {
              this.populateDefaultFields(response.defaultFields);
            }
          } else if (response.status === "error") {
            this.validationResult = response.data;
            this.errorMessage = response.message[0].error || "An error occurred during validation";
            this.populateForm(response.data);
            // Handle defaultFields if present in validation response
            if (response.defaultFields && Array.isArray(response.defaultFields)) {
              this.populateDefaultFields(response.defaultFields);
            }
          }
        },
        error: (error: any) => {
          this.validationResult = error.error?.data || null;
          this.errorMessage = error.error?.message[0].error || "An unexpected error occurred during validation";

          if (this.validationResult) {
            this.populateForm(this.validationResult);
          }
        },
        complete: () => this.isLoading = false
      });
    }
  }

  closeSuccessPopup() {
    this.showSuccessPopup = false;
  }

  private buildFormData(data: any): any {
    const result: { [key: string]: any } = {};
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        const value = data[key];
        if (value === null || value === undefined || value === "") continue;

        if (Array.isArray(value)) {
          const field = this.fields.find(field => field.fieldName === key);
          if (field && field.isMulti) {
            if (typeof value[0] === 'object' && value[0][field.fieldName] !== undefined) {
              result[key] = value.map((item: any) => item[field.fieldName]);
            } else {
              result[key] = value;
            }
          } else {
            const nestedData = value.map((item: any) => {
              if (item.fieldsToAppear) {
                return item.fieldsToAppear;
              } else {
                return this.buildFormData(item);
              }
            });
            if (nestedData.length > 0) {
              result[key] = nestedData;
            }
          }
        } else if (typeof value === "object") {
          const nestedObject = this.buildFormData(value);
          if (Object.keys(nestedObject).length > 0) {
            result[key] = nestedObject;
          }
        } else {
          result[key] = value;
        }
      }
    }
    return result;
  }

  // Helper method to extract part before comma for tables API calls
  private extractTablesApiId(tableName?: string): string {
    const nameToUse = tableName || this.screenName || this.tableName;
    if (nameToUse && nameToUse.includes(',')) {
      return nameToUse.split(',')[0].trim();
    }
    return nameToUse;
  }

  fetchFormData() {
    this.isLoading = true;
    const id = this.form.get("ID")?.value;
    const tablesApiId = this.extractTablesApiId(this.tableName);
    const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}`;
    const params = {
      isTenantBased: this.isTenantBasedFlag.toString(),
    };
    this.http.get(apiUrl, { withCredentials: true, params }).subscribe({
      next: (response: any) => {
        if (response && response.data) {
          this.populateForm(response.data);
        }
        // Handle defaultFields if present in response
        if (response && response.defaultFields && Array.isArray(response.defaultFields)) {
          this.populateDefaultFields(response.defaultFields);
        }
      },
      error: (error) => {
        this.errorMessage = "An error occurred while fetching data";
      },
      complete: () => this.isLoading = false
    });
  }

  populateForm(data: any): void {
    Object.keys(data).forEach(key => {
      const formControl = this.form.get(key);

      if (formControl instanceof FormArray && Array.isArray(data[key])) {
        const formArray = formControl as FormArray;
        formArray.clear();

        if (this.fields.some(field => field.Group === key)) {
          // 🔷 Handle Grouped Fields (e.g., ledger)
          data[key].forEach((groupData: any, groupIndex: number) => {
            const groupForm = this.createGroup(key) as FormGroup;

            // Patch flat fields first (excluding nested arrays)
            const flatGroupData = { ...groupData };
            Object.keys(flatGroupData).forEach(nestedKey => {
              if (Array.isArray(flatGroupData[nestedKey])) {
                delete flatGroupData[nestedKey];
              }
            });
            groupForm.patchValue(flatGroupData);

            // 🔷 Handle nested multi-fields (e.g., denom)
            Object.keys(groupData).forEach(nestedKey => {
              const nestedValue = groupData[nestedKey];
              const nestedControl = groupForm.get(nestedKey);

              if (nestedControl instanceof FormArray && Array.isArray(nestedValue)) {
                nestedControl.clear();

                const nestedFieldMeta = this.fields.find(f =>
                  f.fieldName === nestedKey &&
                  f.Group?.startsWith(key)
                );

                if (nestedFieldMeta) {
                  nestedValue.forEach((item: any, index: number) => {
                    console.log(`📦 Pushing into [${key} > ${nestedKey}] index ${index}:`, item);

                    const nestedGroup = this.createMultiField2(nestedFieldMeta, item); // ← Pass item as second argument
                    nestedGroup.patchValue(item);
                    nestedControl.push(nestedGroup);
                  });
                }
              }
            });

            formArray.push(groupForm);
          });

        } else {
          // 🔶 Handle Flat Multi-Fields
          const field = this.fields.find(field => field.fieldName === key);
          if (field) {
            data[key].forEach((value: any, index: number) => {
              const multiGroup = this.createMultiField(field);
              if (typeof value === 'object' && !Array.isArray(value)) {
                multiGroup.patchValue(value);
              } else {
                multiGroup.patchValue({ [field.fieldName]: value });
              }
              formArray.push(multiGroup);
            });
          }
        }

      } else if (formControl) {
        // For simple fields (not FormArray)
        const field = this.fields.find(field => field.fieldName === key);
        if (field && field.type === 'date' && typeof data[key] === 'string') {
          const parsedDate = new Date(data[key]);
          const dateOnly = parsedDate.toISOString().split('T')[0];
          if (!isNaN(parsedDate.getTime())) {
            formControl.setValue(dateOnly);
          } else {
            formControl.setValue(null);
          }
        } else if (field && field.type === 'date' && Array.isArray(data[key])) {
          // Handle the case where data[key] is an array of date strings (for multi-fields)
          const parsedDates = data[key].map(dateStr => {
            const parsedDate = new Date(dateStr);
            return !isNaN(parsedDate.getTime()) ? parsedDate : null;
          });
          formControl.setValue(parsedDates);
        } else if (field && field.type === 'boolean') {
          // Handle boolean fields properly
          const boolValue = data[key] === true || data[key] === 'true' || data[key] === 1 || data[key] === '1';
          formControl.setValue(boolValue);
        } else if (Array.isArray(data[key])) {
          // Handle array fields (like recordAuthorizer)
          formControl.setValue(data[key].join(', ')); // Convert array to comma-separated string for display
        } else {
          formControl.setValue(data[key]);
        }

        if (this.isViewMode) {
          formControl.disable(); // Disable the control AFTER setting the value
        }
      }
    });
  }
  //populatedefualt fields 
  populateDefaultFields(defaultFields: any[]) {
    if (!Array.isArray(defaultFields)) return;
    defaultFields.forEach(item => {
      if (item && typeof item === 'object') {
        // Handle API structure: { defaultField, defaultValue }
        if ('defaultField' in item && 'defaultValue' in item) {
          const fieldName = item.defaultField;
          const defaultValue = item.defaultValue;
          const formControl = this.form.get(fieldName);
          if (formControl && defaultValue !== null && defaultValue !== undefined) {
            const wasDisabled = formControl.disabled;
            if (wasDisabled) formControl.enable({ emitEvent: false });
            const field = this.fields.find(f => f.fieldName === fieldName);
            if (field && field.type === 'date' && typeof defaultValue === 'string') {
              const parsedDate = new Date(defaultValue);
              if (!isNaN(parsedDate.getTime())) {
                const dateOnly = parsedDate.toISOString().split('T')[0];
                formControl.setValue(dateOnly);
              }
            } else if (field && field.type === 'boolean') {
              const boolValue = defaultValue === true || defaultValue === 'true' || defaultValue === 1 || defaultValue === '1';
              formControl.setValue(boolValue);
            } else if (field && (field.type === 'int' || field.type === 'double')) {
              const numValue = parseFloat(defaultValue);
              if (!isNaN(numValue)) {
                formControl.setValue(numValue);
              }
            } else {
              formControl.setValue(defaultValue);
            }
            if (wasDisabled) formControl.disable({ emitEvent: false });
          }
        } else {
          // Fallback: handle { fieldName: value } structure
          Object.keys(item).forEach(fieldName => {
            const defaultValue = item[fieldName];
            const formControl = this.form.get(fieldName);
            if (formControl && defaultValue !== null && defaultValue !== undefined) {
              const wasDisabled = formControl.disabled;
              if (wasDisabled) formControl.enable({ emitEvent: false });
              const field = this.fields.find(f => f.fieldName === fieldName);
              if (field && field.type === 'date' && typeof defaultValue === 'string') {
                const parsedDate = new Date(defaultValue);
                if (!isNaN(parsedDate.getTime())) {
                  const dateOnly = parsedDate.toISOString().split('T')[0];
                  formControl.setValue(dateOnly);
                }
              } else if (field && field.type === 'boolean') {
                const boolValue = defaultValue === true || defaultValue === 'true' || defaultValue === 1 || defaultValue === '1';
                formControl.setValue(boolValue);
              } else if (field && (field.type === 'int' || field.type === 'double')) {
                const numValue = parseFloat(defaultValue);
                if (!isNaN(numValue)) {
                  formControl.setValue(numValue);
                }
              } else {
                formControl.setValue(defaultValue);
              }
              if (wasDisabled) formControl.disable({ emitEvent: false });
            }
          });
        }
      }
    });
  }

  createMultiField2(fieldMeta: any, sampleData?: any): FormGroup {
    const groupFields: any = {};

    if (sampleData && typeof sampleData === 'object') {
      for (const key of Object.keys(sampleData)) {
        groupFields[key] = '';
      }
    } else {
      groupFields[fieldMeta.fieldName] = '';
    }

    return this.fb.group(groupFields);
  }

  goBack() {
    const id = this.form.get("ID")?.value;
    const tablesApiId = this.extractTablesApiId(this.tableName);
    const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}/force-unlock`;

    this.http.delete(apiUrl, { withCredentials: true }).subscribe({
      next: () => {
        // Record unlocked successfully
      },
      error: (error) => {
        this.cleanupForm();
      },
      complete: () => {
        this.cleanupForm();
      }
    });
  }

  private cleanupForm() {
    // First remove all controls from the form except ID
    Object.keys(this.form.controls).forEach(key => {
      if (key !== 'ID') {
        this.form.removeControl(key);
      }
    });

    // Reset the form
    this.form.reset();

    // Clear all fields and state
    this.fields = [];
    this.showInitialInput = true;
    this.isViewMode = false;
    this.submissionSuccess = false;
    this.validationResult = null;
    this.showValidation = false; // Reset validation state
    this.setFormReadonly(false);

    // Clear all dropdown states
    this.dropdownOptions = {};
    this.dropdownLoaded = {};
    this.dropdownVisible = {};
    this.showIdDropdown = false;
    this.idOptions = [];
    this.filteredIdOptions = [];
    this.showTypeDropdown = {};
    this.filteredTypeOptions = {};
    this.showForeignKeyDropdown = {};
    this.filteredForeignKeyOptions = {};
    this.showRegularDropdown = {};
    this.filteredRegularOptions = {};
  }

  onDropdownChange(event: Event, fieldName: string, groupIndex?: number, multiIndex?: number, groupName?: string) {
    const value = (event.target as HTMLSelectElement).value;

    if (groupName && groupIndex !== undefined) {
      const groupArray = this.getGroupArray(groupName);
      const group = groupArray.at(groupIndex) as FormGroup;

      if (multiIndex !== undefined) {
        const multiArray = group.get(fieldName) as FormArray;
        const multiControl = multiArray.at(multiIndex) as FormGroup;

        if (multiControl instanceof FormGroup) {
          multiControl.setValue({ [fieldName]: value });
        }
      } else {
        const groupControl = group.get(fieldName);
        if (groupControl) {
          groupControl.setValue(value);
        }
      }
    } else {
      const control = this.form.get(fieldName);

      if (multiIndex !== undefined && control instanceof FormArray) {
        const multiArray = control as FormArray;
        const multiControl = multiArray.at(multiIndex) as FormGroup;

        if (multiControl instanceof FormGroup) {
          multiControl.setValue({ [fieldName]: value });
        }
      } else if (control) {
        control.setValue(value);
      }
    }
  }

  getKeys(option: any): string[] {
    return Object.keys(option);
  }

  toggleDropdown(fieldName: string) {
    this.dropdownVisible[fieldName] = !this.dropdownVisible[fieldName];
  }

  toggleViewMode() {
    this.isRowView = !this.isRowView;
  }


  cloneGroup(groupName: string, index: number): void {
    const groupArray = this.getGroupArray(groupName);
    const groupToClone = groupArray.at(index) as FormGroup;

    // Step 1: Add a new empty group using your existing method
    this.addGroup(groupName, index);

    // Step 2: Get the newly inserted group
    const clonedGroup = groupArray.at(index + 1) as FormGroup;

    // Step 3: Copy values (deep clone including nested FormArrays)
    Object.keys(groupToClone.controls).forEach(key => {
      const control = groupToClone.get(key);
      const clonedControl = clonedGroup.get(key);

      if (control instanceof FormArray && clonedControl instanceof FormArray) {
        clonedControl.clear();
        control.controls.forEach(c => {
          const clonedSubGroup = this.fb.group({});
          Object.keys((c as FormGroup).controls).forEach(subKey => {
            clonedSubGroup.addControl(subKey, this.fb.control((c as FormGroup).get(subKey)?.value));
          });
          clonedControl.push(clonedSubGroup);
        });
      } else {
        clonedControl?.setValue(control?.value);
      }
    });
  }

  // ID dropdown methods
  onIdInputChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;

    if (value && value.trim() !== '') {
      this.showValidation = false;
    }

    // Clear previous timeout
    if (this.idSearchTimeout) {
      clearTimeout(this.idSearchTimeout);
    }

    // Set a new timeout to avoid too many API calls
    this.idSearchTimeout = setTimeout(() => {
      this.searchIdOptions(value);
    }, 300);
  }

  getInputClass(): string {
    return this.showValidation ? 'invalid-input' : '';
  }

  onIdInputFocus(): void {
    const currentValue = this.form.get('ID')?.value || '';
    if (currentValue.trim() === '') {
      this.loadAllIds();
    } else {
      this.searchIdOptions(currentValue);
    }
  }

  onIdInputBlur(): void {
    // Delay hiding dropdown to allow click on dropdown items
    setTimeout(() => {
      this.showIdDropdown = false;
    }, 200);
  }

  toggleIdDropdown(): void {
    if (!this.showIdDropdown) {
      const currentValue = this.form.get('ID')?.value || '';
      // If no value is entered, show all IDs
      if (currentValue.trim() === '') {
        this.loadAllIds();
      } else {
        this.searchIdOptions(currentValue);
      }
    } else {
      this.showIdDropdown = false;
    }
  }

  // Helper method to extract part before comma for query-builder API calls
  private extractQueryBuilderId(tableName?: string): string {
    const nameToUse = tableName || this.screenName || this.tableName;
    if (nameToUse && nameToUse.includes(',')) {
      return nameToUse.split(',')[0].trim();
    }
    return nameToUse;
  }

  searchIdOptions(searchTerm: string): void {
    if (!this.tableName && !this.screenName) {
      return;
    }

    const queryBuilderId = this.extractQueryBuilderId();
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const payload = {
      ID: {
        CT: searchTerm // CT = Contains operator
      },
      _select: ["ID"],
      _limit: 20
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.filteredIdOptions = response;
          this.showIdDropdown = true;
        } else {
          this.filteredIdOptions = [];
          this.showIdDropdown = true; // Show dropdown with "No IDs found" message
        }
      },
      error: (error) => {
        this.filteredIdOptions = [];
        this.showIdDropdown = true; // Show dropdown with "No IDs found" message
      }
    });
  }

  selectIdOption(option: any): void {
    this.form.get('ID')?.setValue(option.ID);
    this.showIdDropdown = false;
    // Reset validation state when a valid ID is selected
    this.showValidation = false;
  }

  loadAllIds(): void {
    if (!this.tableName && !this.screenName) {
      return;
    }

    // Use the query-builder API to get all IDs
    const queryBuilderId = this.extractQueryBuilderId();
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const payload = {
      _select: ["ID"],
      _limit: 100 // Higher limit to get more IDs
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.filteredIdOptions = response;
          this.showIdDropdown = true;
        } else {
          this.filteredIdOptions = [];
          this.showIdDropdown = true; // Show dropdown even if no data to show "No IDs found"
        }
      },
      error: (error) => {
        this.filteredIdOptions = [];
        this.showIdDropdown = true;
      }
    });
  }






















  get idField(): any {
    return this.fields.find(f => f.fieldName?.toUpperCase() === 'ID');
  }

  isIdValid(): boolean {
    const idValue = this.form.get('ID')?.value;
    return idValue && idValue.trim() !== '';
  }

  rejectRecord(): void {
    // TODO: Implement reject functionality
    console.log('Reject record');
  }

  deleteRecord(): void {
    // TODO: Implement delete functionality
    console.log('Delete record');
  }

  onDropdownOptionSelected(event: {option: any, fieldName: string}): void {
    // Handle dropdown option selection
    // The dropdown component already sets the form value, so we just need to handle any additional logic
    console.log('Dropdown option selected:', event);
  }

  onMultiFieldAdded(event: {field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number}): void {
    // Handle multi-field addition
    console.log('Multi-field added:', event);
  }

  onMultiFieldRemoved(event: {fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number}): void {
    // Handle multi-field removal
    console.log('Multi-field removed:', event);
  }

  onGroupAdded(event: {groupName: string, index: number}): void {
    // Handle group addition
    console.log('Group added:', event);
  }

  onGroupRemoved(event: {groupName: string, index: number}): void {
    // Handle group removal
    console.log('Group removed:', event);
  }

  onGroupCloned(event: {groupName: string, index: number}): void {
    // Handle group cloning
    console.log('Group cloned:', event);
  }



}

