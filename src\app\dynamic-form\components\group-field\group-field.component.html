<div [formArrayName]="getGroupName()" class="grouped-field-section">
  <h3>{{ getGroupName() }}</h3>
  <ng-container *ngFor="let group of formArray.controls; let k = index; trackBy: trackByIndex">
    <div [formGroupName]="k" [class]="isRowView ? 'form-grid row-view-table' : 'form-grid multi-field'">
      <ng-container *ngIf="isRowView; else nestedView">
        <div class="row-view-table-container">
          <ng-container *ngFor="let groupField of getFieldsForGroup(getGroupName())">
            <ng-container *ngIf="!groupField.isMulti">
              <div class="row-view-table-cell">
                <label>{{ groupField.fieldName }} <span *ngIf="groupField.mandatory">*</span><span *ngIf="groupField.noInput" class="no-input-indicator"> (Read Only)</span></label>
                <ng-container *ngIf="groupField.foreginKey; else regularInput">
                  <ng-container *ngIf="groupField.foreginKey === 'fieldType'; else foreignKeyInput">
                    <div class="dropdown-input-container">
                      <input [formControlName]="groupField.fieldName" type="text" class="form-input dropdown-input"
                        (input)="typeInputChange.emit({event: $event, fieldName: groupField.fieldName + '_group_' + k})"
                        (focus)="typeInputFocus.emit(groupField.fieldName + '_group_' + k)"
                        (blur)="typeInputBlur.emit(groupField.fieldName + '_group_' + k)"
                        [disabled]="groupField.noInput"
                        [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />
                      <button type="button" class="dropdown-arrow-btn"
                        (click)="toggleTypeDropdown.emit(groupField.fieldName + '_group_' + k)"
                        [disabled]="groupField.noInput" matTooltip="Show type suggestions">
                        <mat-icon>{{ showTypeDropdown[groupField.fieldName + '_group_' + k] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                      </button>
                                              <div *ngIf="showTypeDropdown[groupField.fieldName + '_group_' + k]" class="dropdown-list">
                          <ng-container *ngIf="filteredTypeOptions[groupField.fieldName + '_group_' + k] && filteredTypeOptions[groupField.fieldName + '_group_' + k].length > 0; else noTypes">
                          <div *ngFor="let option of filteredTypeOptions[groupField.fieldName + '_group_' + k] || []; trackBy: trackByIndex" class="dropdown-item" (click)="selectTypeOption.emit({option, fieldName: groupField.fieldName + '_group_' + k})">
                            {{ option.ROW_ID }}
                          </div>
                        </ng-container>
                        <ng-template #noTypes><div class="dropdown-empty">No types found</div></ng-template>
                      </div>
                    </div>
                  </ng-container>
                  <ng-template #foreignKeyInput>
                    <ng-container *ngIf="groupField.foreginKey === 'formDefinition'; else regularDropdown">
                      <div class="dropdown-input-container">
                        <input [formControlName]="groupField.fieldName" type="text" class="form-input dropdown-input"
                          (input)="foreignKeyInputChange.emit({event: $event, fieldName: groupField.fieldName + '_group_' + k})"
                          (focus)="foreignKeyInputFocus.emit(groupField.fieldName + '_group_' + k)"
                          (blur)="foreignKeyInputBlur.emit(groupField.fieldName + '_group_' + k)"
                          [disabled]="groupField.noInput"
                          [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />
                        <button type="button" class="dropdown-arrow-btn"
                          (click)="toggleForeignKeyDropdown.emit(groupField.fieldName + '_group_' + k)"
                          [disabled]="groupField.noInput" matTooltip="Show foreign key suggestions">
                          <mat-icon>{{ showForeignKeyDropdown[groupField.fieldName + '_group_' + k] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                        </button>
                        <div *ngIf="showForeignKeyDropdown[groupField.fieldName + '_group_' + k]" class="dropdown-list">
                          <ng-container *ngIf="filteredForeignKeyOptions[groupField.fieldName + '_group_' + k] && filteredForeignKeyOptions[groupField.fieldName + '_group_' + k].length > 0; else noForeignKeys">
                            <div *ngFor="let option of filteredForeignKeyOptions[groupField.fieldName + '_group_' + k] || []; trackBy: trackByIndex" class="dropdown-item" (click)="selectForeignKeyOption.emit({option, fieldName: groupField.fieldName + '_group_' + k})">
                              {{ option.ROW_ID }}
                            </div>
                          </ng-container>
                          <ng-template #noForeignKeys><div class="dropdown-empty">No foreign keys found</div></ng-template>
                        </div>
                      </div>
                    </ng-container>
                    <ng-template #regularDropdown>
                      <div class="dropdown-input-container">
                        <input [formControlName]="groupField.fieldName" type="text" class="form-input dropdown-input"
                          (input)="regularInputChange.emit({event: $event, fieldName: groupField.fieldName + '_group_' + k})"
                          (focus)="regularInputFocus.emit(groupField.fieldName + '_group_' + k)"
                          (blur)="regularInputBlur.emit(groupField.fieldName + '_group_' + k)"
                          [disabled]="groupField.noInput"
                          [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />
                        <button type="button" class="dropdown-arrow-btn"
                          (click)="toggleRegularDropdown.emit(groupField.fieldName + '_group_' + k)"
                          [disabled]="groupField.noInput" matTooltip="Show options">
                          <mat-icon>{{ showRegularDropdown[groupField.fieldName + '_group_' + k] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                        </button>
                        <div *ngIf="showRegularDropdown[groupField.fieldName + '_group_' + k]" class="dropdown-list">
                          <ng-container *ngIf="filteredRegularOptions[groupField.fieldName + '_group_' + k] && filteredRegularOptions[groupField.fieldName + '_group_' + k].length > 0; else noOptions">
                            <div *ngFor="let option of filteredRegularOptions[groupField.fieldName + '_group_' + k] || []; trackBy: trackByIndex" class="dropdown-item" (click)="selectRegularOption.emit({option, fieldName: groupField.fieldName + '_group_' + k})">
                              <ng-container *ngFor="let key of getKeys(option)">{{ option[key] }}&nbsp;</ng-container>
                            </div>
                          </ng-container>
                          <ng-template #noOptions><div class="dropdown-empty">No options found</div></ng-template>
                        </div>
                      </div>
                    </ng-template>
                  </ng-template>
                </ng-container>
                <ng-template #regularInput>
                  <ng-container [ngSwitch]="groupField.type">
                    <input *ngSwitchCase="'string'" [formControlName]="groupField.fieldName" type="text" [placeholder]="(groupField.label?.trim() || groupField.fieldName)" [disabled]="groupField.noInput" />
                    <input *ngSwitchCase="'int'" [formControlName]="groupField.fieldName" type="number" [disabled]="groupField.noInput" />
                    <input *ngSwitchCase="'boolean'" [formControlName]="groupField.fieldName" type="checkbox" [disabled]="groupField.noInput" />
                    <input *ngSwitchCase="'date'" [formControlName]="groupField.fieldName" type="date" [disabled]="groupField.noInput" />
                    <input *ngSwitchCase="'double'" [formControlName]="groupField.fieldName" type="number" step="00.50" [disabled]="groupField.noInput" />
                  </ng-container>
                </ng-template>
              </div>
            </ng-container>
            <!-- Multi-fields for parent group -->
            <ng-container *ngIf="groupField.isMulti">
              <div class="row-view-table-cell-multi" [formArrayName]="groupField.fieldName">
                <label>{{ groupField.fieldName }} <span *ngIf="groupField.noInput" class="no-input-indicator"> (Read Only)</span></label>
                <ng-container *ngFor="let multiControl of getMultiArray(groupField.fieldName, k, getGroupName()).controls; let l = index; trackBy: trackByIndex">
                  <div [formGroupName]="l" class="row-view-multi-item">
                    <div class="row-view-multi-input">
                      <ng-container [ngSwitch]="groupField.type">
                        <input *ngSwitchCase="'string'" [formControlName]="groupField.fieldName" type="text" [placeholder]="(groupField.label?.trim() || groupField.fieldName) + ' (' + (l + 1) + ')'" [disabled]="groupField.noInput" />
                        <input *ngSwitchCase="'int'" [formControlName]="groupField.fieldName" type="number" [disabled]="groupField.noInput" />
                        <input *ngSwitchCase="'boolean'" [formControlName]="groupField.fieldName" type="checkbox" [disabled]="groupField.noInput" />
                        <input *ngSwitchCase="'date'" [formControlName]="groupField.fieldName" type="date" [disabled]="groupField.noInput" />
                        <input *ngSwitchCase="'double'" [formControlName]="groupField.fieldName" type="number" step="0.01" [disabled]="groupField.noInput" />
                      </ng-container>
                      <ng-container *ngIf="groupField.foreginKey">
                        <div class="dropdown-input-container">
                          <input [formControlName]="groupField.fieldName" type="text" class="form-input dropdown-input"
                            (input)="regularInputChange.emit({event: $event, fieldName: groupField.fieldName + '_group_' + k + '_multi_' + l})"
                            (focus)="regularInputFocus.emit(groupField.fieldName + '_group_' + k + '_multi_' + l)"
                            (blur)="regularInputBlur.emit(groupField.fieldName + '_group_' + k + '_multi_' + l)"
                            [disabled]="groupField.noInput"
                            [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />
                          <button type="button" class="dropdown-arrow-btn"
                            (click)="toggleRegularDropdown.emit(groupField.fieldName + '_group_' + k + '_multi_' + l)"
                            [disabled]="groupField.noInput" matTooltip="Show options">
                            <mat-icon>{{ showRegularDropdown[groupField.fieldName + '_group_' + k + '_multi_' + l] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                          </button>
                          <div *ngIf="showRegularDropdown[groupField.fieldName + '_group_' + k + '_multi_' + l]" class="dropdown-list">
                            <ng-container *ngIf="filteredRegularOptions[groupField.fieldName + '_group_' + k + '_multi_' + l] && filteredRegularOptions[groupField.fieldName + '_group_' + k + '_multi_' + l].length > 0; else noOptionsMulti">
                              <div *ngFor="let option of filteredRegularOptions[groupField.fieldName + '_group_' + k + '_multi_' + l] || []; trackBy: trackByIndex" class="dropdown-item" (click)="selectRegularOption.emit({option, fieldName: groupField.fieldName + '_group_' + k + '_multi_' + l})">
                                <ng-container *ngFor="let key of getKeys(option)">{{ option[key] }}&nbsp;</ng-container>
                              </div>
                            </ng-container>
                            <ng-template #noOptionsMulti><div class="dropdown-empty">No options found</div></ng-template>
                          </div>
                        </div>
                      </ng-container>
                    </div>
                    <div class="row-view-multi-buttons">
                      <button *ngIf="getMultiArray(groupField.fieldName, k, getGroupName()).controls.length > 1 && !isViewMode && !groupField.noInput" mat-icon-button color="warn" type="button" (click)="removeMultiField.emit({fieldName: groupField.fieldName, index: l, groupIndex: k, groupName: getGroupName()})" matTooltip="Delete">
                        <mat-icon>delete</mat-icon>
                      </button>
                      <button *ngIf="!isViewMode && !groupField.noInput" mat-icon-button color="primary" type="button" (click)="addMultiField.emit({field: groupField, groupIndex: k, index: l, groupName: getGroupName()})" matTooltip="Add">
                        <mat-icon>add</mat-icon>
                      </button>
                    </div>
                  </div>
                </ng-container>
              </div>
            </ng-container>
          </ng-container>
          <div class="group-buttons">
            <button mat-icon-button color="primary" type="button" (click)="addGroup.emit({groupName: getGroupName(), index: k})" matTooltip="Add Group">
              <mat-icon>add</mat-icon>
            </button>
            <button mat-icon-button color="warn" type="button" (click)="removeGroup.emit({groupName: getGroupName(), index: k})" matTooltip="Remove Group">
              <mat-icon>delete</mat-icon>
            </button>
            <button mat-icon-button color="accent" type="button" (click)="cloneGroup.emit({groupName: getGroupName(), index: k})" matTooltip="Clone Group">
              <mat-icon>content_copy</mat-icon>
            </button>
          </div>
        </div>
      </ng-container>
      <ng-template #nestedView>
        <div [class]="isRowView ? 'group-fields row-view-fields' : 'group-fields'">
          <ng-container *ngFor="let groupField of getFieldsForGroup(getGroupName())">
            <ng-container *ngIf="!groupField.isMulti">
              <div class="form-field">
                <label>{{ groupField.fieldName }} <span *ngIf="groupField.mandatory">*</span><span *ngIf="groupField.noInput" class="no-input-indicator"> (Read Only)</span></label>
                <ng-container *ngIf="groupField.foreginKey; else regularInputNested">
                  <ng-container *ngIf="groupField.foreginKey === 'fieldType'; else foreignKeyInputNested">
                    <div class="dropdown-input-container">
                      <input [formControlName]="groupField.fieldName" type="text" class="form-input dropdown-input"
                        (input)="typeInputChange.emit({event: $event, fieldName: groupField.fieldName + '_group_' + k})"
                        (focus)="typeInputFocus.emit(groupField.fieldName + '_group_' + k)"
                        (blur)="typeInputBlur.emit(groupField.fieldName + '_group_' + k)"
                        [disabled]="groupField.noInput"
                        [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />
                      <button type="button" class="dropdown-arrow-btn"
                        (click)="toggleTypeDropdown.emit(groupField.fieldName + '_group_' + k)"
                        [disabled]="groupField.noInput" matTooltip="Show type suggestions">
                        <mat-icon>{{ showTypeDropdown[groupField.fieldName + '_group_' + k] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                      </button>
                      <div *ngIf="showTypeDropdown[groupField.fieldName + '_group_' + k]" class="dropdown-list">
                        <ng-container *ngIf="filteredTypeOptions[groupField.fieldName + '_group_' + k] && filteredTypeOptions[groupField.fieldName + '_group_' + k].length > 0; else noTypesNested">
                          <div *ngFor="let option of filteredTypeOptions[groupField.fieldName + '_group_' + k] || []; trackBy: trackByIndex" class="dropdown-item" (click)="selectTypeOption.emit({option, fieldName: groupField.fieldName + '_group_' + k})">
                            {{ option.ROW_ID }}
                          </div>
                        </ng-container>
                        <ng-template #noTypesNested><div class="dropdown-empty">No types found</div></ng-template>
                      </div>
                    </div>
                  </ng-container>
                  <ng-template #foreignKeyInputNested>
                    <ng-container *ngIf="groupField.foreginKey === 'formDefinition'; else regularDropdownNested">
                      <div class="dropdown-input-container">
                        <input [formControlName]="groupField.fieldName" type="text" class="form-input dropdown-input"
                          (input)="foreignKeyInputChange.emit({event: $event, fieldName: groupField.fieldName + '_group_' + k})"
                          (focus)="foreignKeyInputFocus.emit(groupField.fieldName + '_group_' + k)"
                          (blur)="foreignKeyInputBlur.emit(groupField.fieldName + '_group_' + k)"
                          [disabled]="groupField.noInput"
                          [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />
                        <button type="button" class="dropdown-arrow-btn"
                          (click)="toggleForeignKeyDropdown.emit(groupField.fieldName + '_group_' + k)"
                          [disabled]="groupField.noInput" matTooltip="Show foreign key suggestions">
                          <mat-icon>{{ showForeignKeyDropdown[groupField.fieldName + '_group_' + k] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                        </button>
                        <div *ngIf="showForeignKeyDropdown[groupField.fieldName + '_group_' + k]" class="dropdown-list">
                          <ng-container *ngIf="filteredForeignKeyOptions[groupField.fieldName + '_group_' + k] && filteredForeignKeyOptions[groupField.fieldName + '_group_' + k].length > 0; else noForeignKeysNested">
                            <div *ngFor="let option of filteredForeignKeyOptions[groupField.fieldName + '_group_' + k] || []; trackBy: trackByIndex" class="dropdown-item" (click)="selectForeignKeyOption.emit({option, fieldName: groupField.fieldName + '_group_' + k})">
                              {{ option.ROW_ID }}
                            </div>
                          </ng-container>
                          <ng-template #noForeignKeysNested><div class="dropdown-empty">No foreign keys found</div></ng-template>
                        </div>
                      </div>
                    </ng-container>
                    <ng-template #regularDropdownNested>
                      <div class="dropdown-input-container">
                        <input [formControlName]="groupField.fieldName" type="text" class="form-input dropdown-input"
                          (input)="regularInputChange.emit({event: $event, fieldName: groupField.fieldName + '_group_' + k})"
                          (focus)="regularInputFocus.emit(groupField.fieldName + '_group_' + k)"
                          (blur)="regularInputBlur.emit(groupField.fieldName + '_group_' + k)"
                          [disabled]="groupField.noInput"
                          [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />
                        <button type="button" class="dropdown-arrow-btn"
                          (click)="toggleRegularDropdown.emit(groupField.fieldName + '_group_' + k)"
                          [disabled]="groupField.noInput" matTooltip="Show options">
                          <mat-icon>{{ showRegularDropdown[groupField.fieldName + '_group_' + k] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                        </button>
                        <div *ngIf="showRegularDropdown[groupField.fieldName + '_group_' + k]" class="dropdown-list">
                          <ng-container *ngIf="filteredRegularOptions[groupField.fieldName + '_group_' + k] && filteredRegularOptions[groupField.fieldName + '_group_' + k].length > 0; else noOptionsNested">
                            <div *ngFor="let option of filteredRegularOptions[groupField.fieldName + '_group_' + k] || []; trackBy: trackByIndex" class="dropdown-item" (click)="selectRegularOption.emit({option, fieldName: groupField.fieldName + '_group_' + k})">
                              <ng-container *ngFor="let key of getKeys(option)">{{ option[key] }}&nbsp;</ng-container>
                            </div>
                          </ng-container>
                          <ng-template #noOptionsNested><div class="dropdown-empty">No options found</div></ng-template>
                        </div>
                      </div>
                    </ng-template>
                  </ng-template>
                </ng-container>
                <ng-template #regularInputNested>
                  <ng-container [ngSwitch]="groupField.type">
                    <input *ngSwitchCase="'string'" [formControlName]="groupField.fieldName" type="text" [placeholder]="(groupField.label?.trim() || groupField.fieldName)" [disabled]="groupField.noInput" />
                    <input *ngSwitchCase="'int'" [formControlName]="groupField.fieldName" type="number" [disabled]="groupField.noInput" />
                    <input *ngSwitchCase="'boolean'" [formControlName]="groupField.fieldName" type="checkbox" [disabled]="groupField.noInput" />
                    <input *ngSwitchCase="'date'" [formControlName]="groupField.fieldName" type="date" [disabled]="groupField.noInput" />
                    <input *ngSwitchCase="'double'" [formControlName]="groupField.fieldName" type="number" step="00.50" [disabled]="groupField.noInput" />
                  </ng-container>
                </ng-template>
              </div>
            </ng-container>
            <!-- Multi-fields for parent group -->
            <ng-container *ngIf="groupField.isMulti">
              <div class="form-field is-multi" [formArrayName]="groupField.fieldName">
                <label>{{ groupField.fieldName }} <span *ngIf="groupField.noInput" class="no-input-indicator"> (Read Only)</span></label>
                <ng-container *ngFor="let multiControl of getMultiArray(groupField.fieldName, k, getGroupName()).controls; let l = index; trackBy: trackByIndex">
                  <div [formGroupName]="l" class="multi-input-container">
                    <ng-container [ngSwitch]="groupField.type">
                      <input *ngSwitchCase="'string'" [formControlName]="groupField.fieldName" type="text" [placeholder]="(groupField.label?.trim() || groupField.fieldName) + ' (' + (l + 1) + ')'" [disabled]="groupField.noInput" />
                      <input *ngSwitchCase="'int'" [formControlName]="groupField.fieldName" type="number" [disabled]="groupField.noInput" />
                      <input *ngSwitchCase="'boolean'" [formControlName]="groupField.fieldName" type="checkbox" [disabled]="groupField.noInput" />
                      <input *ngSwitchCase="'date'" [formControlName]="groupField.fieldName" type="date" [disabled]="groupField.noInput" />
                      <input *ngSwitchCase="'double'" [formControlName]="groupField.fieldName" type="number" step="0.01" [disabled]="groupField.noInput" />
                    </ng-container>
                    <ng-container *ngIf="groupField.foreginKey">
                      <div class="dropdown-input-container">
                        <input [formControlName]="groupField.fieldName" type="text" class="form-input dropdown-input"
                          (input)="regularInputChange.emit({event: $event, fieldName: groupField.fieldName + '_group_' + k + '_multi_' + l})"
                          (focus)="regularInputFocus.emit(groupField.fieldName + '_group_' + k + '_multi_' + l)"
                          (blur)="regularInputBlur.emit(groupField.fieldName + '_group_' + k + '_multi_' + l)"
                          [disabled]="groupField.noInput"
                          [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />
                        <button type="button" class="dropdown-arrow-btn"
                          (click)="toggleRegularDropdown.emit(groupField.fieldName + '_group_' + k + '_multi_' + l)"
                          [disabled]="groupField.noInput" matTooltip="Show options">
                          <mat-icon>{{ showRegularDropdown[groupField.fieldName + '_group_' + k + '_multi_' + l] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                        </button>
                        <div *ngIf="showRegularDropdown[groupField.fieldName + '_group_' + k + '_multi_' + l]" class="dropdown-list">
                          <ng-container *ngIf="filteredRegularOptions[groupField.fieldName + '_group_' + k + '_multi_' + l] && filteredRegularOptions[groupField.fieldName + '_group_' + k + '_multi_' + l].length > 0; else noOptionsMultiNested">
                            <div *ngFor="let option of filteredRegularOptions[groupField.fieldName + '_group_' + k + '_multi_' + l] || []; trackBy: trackByIndex" class="dropdown-item" (click)="selectRegularOption.emit({option, fieldName: groupField.fieldName + '_group_' + k + '_multi_' + l})">
                              <ng-container *ngFor="let key of getKeys(option)">{{ option[key] }}&nbsp;</ng-container>
                            </div>
                          </ng-container>
                          <ng-template #noOptionsMultiNested><div class="dropdown-empty">No options found</div></ng-template>
                        </div>
                      </div>
                    </ng-container>
                    <div class="multi-buttons">
                      <button *ngIf="getMultiArray(groupField.fieldName, k, getGroupName()).length > 1 && !isViewMode && !groupField.noInput" mat-icon-button color="warn" type="button" (click)="removeMultiField.emit({fieldName: groupField.fieldName, index: l, groupIndex: k, groupName: getGroupName()})" matTooltip="Delete">
                        <mat-icon>delete</mat-icon>
                      </button>
                      <button *ngIf="!isViewMode && !groupField.noInput" mat-icon-button color="primary" type="button" (click)="addMultiField.emit({field: groupField, groupIndex: k, index: l, groupName: getGroupName()})" matTooltip="Add">
                        <mat-icon>add</mat-icon>
                      </button>
                    </div>
                  </div>
                </ng-container>
              </div>
            </ng-container>
          </ng-container>
          <div class="group-buttons">
            <button mat-icon-button color="primary" type="button" (click)="addGroup.emit({groupName: getGroupName(), index: k})" matTooltip="Add Group">
              <mat-icon>add</mat-icon>
            </button>
            <button mat-icon-button color="warn" type="button" (click)="removeGroup.emit({groupName: getGroupName(), index: k})" matTooltip="Remove Group">
              <mat-icon>delete</mat-icon>
            </button>
            <button mat-icon-button color="accent" type="button" (click)="cloneGroup.emit({groupName: getGroupName(), index: k})" matTooltip="Clone Group">
              <mat-icon>content_copy</mat-icon>
            </button>
          </div>
        </div>
      </ng-template>
    </div>
  </ng-container>
</div> 