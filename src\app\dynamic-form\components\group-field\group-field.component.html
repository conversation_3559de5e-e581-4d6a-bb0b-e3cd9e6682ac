<div [formArrayName]="parsedGroup.parent" class="grouped-field-section">
  <h3>{{ parsedGroup.parent }}</h3>
  @for (group of groupArray.controls; track group; let k = $index) {
    <div [formGroupName]="k" [class]="isRowView ? 'form-grid row-view-table' : 'form-grid multi-field'">

      @if (isRowView) {
        <!-- Row View: All fields in a single table-like row -->
        <div class="row-view-table-container">
          @for (groupField of getFieldsForGroup(parsedGroup.parent!); track groupField.fieldName) {
            @if (!groupField.isMulti) {
              <div class="row-view-table-cell">
                <!-- Use field-renderer component for grouped fields -->
                <app-field-renderer
                  [field]="groupField"
                  [form]="form"
                  [isViewMode]="isViewMode"
                  [fieldSuffix]="'group_' + k">
                </app-field-renderer>
              </div>
            }
          }
        </div>
      } @else {
        <!-- Nested View: Fields in their natural groups -->
        @for (groupField of getFieldsForGroup(parsedGroup.parent!); track groupField.fieldName) {
          @if (!groupField.isMulti) {
            <div class="form-field">
              <!-- Use field-renderer component for regular grouped fields -->
              <app-field-renderer
                [field]="groupField"
                [form]="form"
                [isViewMode]="isViewMode"
                [fieldSuffix]="'group_' + k">
              </app-field-renderer>
            </div>
          }
        }
      }

      <!-- Group action buttons -->
      <div class="button-group">
        @if (canRemoveGroup()) {
          <button mat-icon-button color="warn" type="button" (click)="removeGroup(parsedGroup.parent!, k)" matTooltip="Delete Group">
            <mat-icon>delete</mat-icon>
          </button>
        }

        @if (canAddGroup()) {
          <button mat-icon-button color="primary" type="button" (click)="addGroup(parsedGroup.parent!, k)" matTooltip="Add Group">
            <mat-icon>add</mat-icon>
          </button>
        }

        @if (canCloneGroup()) {
          <button mat-icon-button color="accent" type="button" (click)="cloneGroup(parsedGroup.parent!, k)" matTooltip="Clone Group">
            <mat-icon>content_copy</mat-icon>
          </button>
        }
      </div>
    </div>
  }
</div>