<div [formArrayName]="field.fieldName">
  @for (control of formArray.controls; track control; let j = $index) {
    <div [formGroupName]="j" class="form-field is-multi">
      <label>{{ field.fieldName }} ({{ j + 1 }})
        @if (field.noInput) {
          <span class="no-input-indicator"> (Read Only)</span>
        }
      </label>
      <div class="multi-input-container">
        <div class="multi-input">
          @if (field.foreginKey) {
            <!-- Check if this is a type field (fieldType) -->
            @if (field.foreginKey === 'fieldType') {
              <div class="dropdown-input-container">
                <input [formControlName]="field.fieldName" type="text" 
                       class="form-input dropdown-input" 
                       (input)="typeInputChange.emit({event: $event, fieldName: getFieldName(j)})" 
                       (focus)="typeInputFocus.emit(getFieldName(j))" 
                       (blur)="typeInputBlur.emit(getFieldName(j))"
                       [disabled]="field.noInput"
                       [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
                
                <button type="button" class="dropdown-arrow-btn" 
                        (click)="toggleTypeDropdown.emit(getFieldName(j))" 
                        [disabled]="field.noInput"
                        matTooltip="Show type suggestions">
                  <mat-icon>{{ showTypeDropdown[getFieldName(j)] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                </button>
                
                @if (showTypeDropdown[getFieldName(j)]) {
                  <div class="dropdown-list">
                    @if (filteredTypeOptions[getFieldName(j)] && filteredTypeOptions[getFieldName(j)].length > 0) {
                      @for (option of filteredTypeOptions[getFieldName(j)]; track option.ROW_ID) {
                        <div class="dropdown-item" (click)="selectTypeOption.emit({option: option, fieldName: getFieldName(j)})">
                          {{ option.ROW_ID }}
                        </div>
                      }
                    } @else {
                      <div class="dropdown-empty">
                        No types found
                      </div>
                    }
                  </div>
                }
              </div>
            }
            <!-- Check if this is a foreign key field (formDefinition) -->
            @else if (field.foreginKey === 'formDefinition') {
              <div class="dropdown-input-container">
                <input [formControlName]="field.fieldName" type="text" 
                       class="form-input dropdown-input" 
                       (input)="foreignKeyInputChange.emit({event: $event, fieldName: getFieldName(j)})" 
                       (focus)="foreignKeyInputFocus.emit(getFieldName(j))" 
                       (blur)="foreignKeyInputBlur.emit(getFieldName(j))"
                       [disabled]="field.noInput"
                       [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
                
                <button type="button" class="dropdown-arrow-btn" 
                        (click)="toggleForeignKeyDropdown.emit(getFieldName(j))" 
                        [disabled]="field.noInput"
                        matTooltip="Show foreign key suggestions">
                  <mat-icon>{{ showForeignKeyDropdown[getFieldName(j)] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                </button>
                
                @if (showForeignKeyDropdown[getFieldName(j)]) {
                  <div class="dropdown-list">
                    @if (filteredForeignKeyOptions[getFieldName(j)] && filteredForeignKeyOptions[getFieldName(j)].length > 0) {
                      @for (option of filteredForeignKeyOptions[getFieldName(j)]; track option.ROW_ID) {
                        <div class="dropdown-item" (click)="selectForeignKeyOption.emit({option: option, fieldName: getFieldName(j)})">
                          {{ option.ROW_ID }}
                        </div>
                      }
                    } @else {
                      <div class="dropdown-empty">
                        No foreign keys found
                      </div>
                    }
                  </div>
                }
              </div>
            }
            <!-- Default dropdown for other foreign keys -->
            @else {
              <div class="dropdown-input-container">
                <input [formControlName]="field.fieldName" type="text" 
                       class="form-input dropdown-input" 
                       (input)="regularInputChange.emit({event: $event, fieldName: getFieldName(j)})" 
                       (focus)="regularInputFocus.emit(getFieldName(j))" 
                       (blur)="regularInputBlur.emit(getFieldName(j))"
                       [disabled]="field.noInput"
                       [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
                
                <button type="button" class="dropdown-arrow-btn" 
                        (click)="toggleRegularDropdown.emit(getFieldName(j))" 
                        [disabled]="field.noInput"
                        matTooltip="Show options">
                  <mat-icon>{{ showRegularDropdown[getFieldName(j)] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                </button>
                
                @if (showRegularDropdown[getFieldName(j)]) {
                  <div class="dropdown-list">
                    @if (filteredRegularOptions[getFieldName(j)] && filteredRegularOptions[getFieldName(j)].length > 0) {
                      @for (option of filteredRegularOptions[getFieldName(j)]; track option.ROW_ID) {
                        <div class="dropdown-item" (click)="selectRegularOption.emit({option: option, fieldName: getFieldName(j)})">
                          @for (key of getKeys(option); track key) {
                            {{ option[key] }}&nbsp;
                          }
                        </div>
                      }
                    } @else {
                      <div class="dropdown-empty">
                        No options found
                      </div>
                    }
                  </div>
                }
              </div>
            }
          } @else {
            @if (field.type === 'string') {
              <input [formControlName]="field.fieldName" type="text"
                [placeholder]="(field.label?.trim() || field.fieldName) + '-' + (j+1)" [disabled]="field.noInput" />
            }
            @if (field.type === 'int') {
              <input [formControlName]="field.fieldName" type="number" [disabled]="field.noInput" />
            }
            @if (field.type === 'boolean') {
              <input [formControlName]="field.fieldName" type="checkbox" [disabled]="field.noInput" />
            }
            @if (field.type === 'date') {
              <input [formControlName]="field.fieldName" type="date" [disabled]="field.noInput" />
            }
            @if (field.type === 'double') {
              <input [formControlName]="field.fieldName" type="number"
                step="00.50" [disabled]="field.noInput" />
            }
          }
        </div>
        <div class="multi-buttons">
          @if (formArray.length > 1 && !isViewMode && !field.noInput) {
            <button mat-icon-button color="warn" type="button" (click)="removeMultiField.emit(j)" matTooltip="Delete">
              <mat-icon>delete</mat-icon>
            </button>
          }
          @if (!isViewMode && !field.noInput) {
            <button mat-icon-button color="primary" type="button" (click)="addMultiField.emit({field: field, index: j})" matTooltip="Add">
              <mat-icon>add</mat-icon>
            </button>
          }
        </div>
      </div>
    </div>
  }
</div> 