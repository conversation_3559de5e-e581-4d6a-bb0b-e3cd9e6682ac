<div [formArrayName]="field.fieldName">
  @for (control of formArray.controls; track control; let j = $index) {
    <div [formGroupName]="j" class="form-field is-multi">
      <label>{{ field.fieldName }} ({{ j + 1 }})
        @if (field.noInput) {
          <span class="no-input-indicator"> (Read Only)</span>
        }
      </label>

      <div class="multi-input-container">
        <div class="multi-input">
          <!-- Use field-renderer component for multi-fields -->
          <app-field-renderer
            [field]="field"
            [form]="form"
            [isViewMode]="isViewMode"
            [fieldSuffix]="j.toString()"
            [isMultiField]="true"
            [multiIndex]="j">
          </app-field-renderer>
        </div>

        <div class="multi-buttons">
          @if (canRemove()) {
            <button mat-icon-button color="warn" type="button" (click)="removeMultiField(j)" matTooltip="Delete">
              <mat-icon>delete</mat-icon>
            </button>
          }

          @if (canAdd()) {
            <button mat-icon-button color="primary" type="button" (click)="addMultiField(j)" matTooltip="Add">
              <mat-icon>add</mat-icon>
            </button>
          }
        </div>
      </div>
    </div>
  }
</div>