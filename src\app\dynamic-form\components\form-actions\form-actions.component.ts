import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-form-actions',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './form-actions.component.html',
  styleUrls: ['./form-actions.component.scss']
})
export class FormActionsComponent {
  @Input() isViewMode: boolean = false;
  @Input() isRowView: boolean = false;
  @Input() errorMessage: string = '';
  @Input() idValue: string = '';
  @Output() toggleViewMode = new EventEmitter<void>();
  @Output() onSubmit = new EventEmitter<void>();
  @Output() validateRecord = new EventEmitter<void>();
  @Output() authorizeRecord = new EventEmitter<void>();
  @Output() goBack = new EventEmitter<void>();
  @Output() rejectRecord = new EventEmitter<void>();
  @Output() deleteRecord = new EventEmitter<void>();
} 