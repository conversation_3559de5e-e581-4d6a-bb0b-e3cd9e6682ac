.dynamic-form-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  max-width: 100%;
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  background-color: white;
}

.form-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  background-color: white;
}

.form-field {
  width: 100%;
  margin-bottom: 16px;
  position: relative;
}

.form-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 12px;
  color: #000000;
}

.form-field input,
.form-field select {
  width: 100%;
  max-width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #ced4da;
  border-radius: 8px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  box-sizing: border-box;
}

.form-field input:focus,
.form-field select:focus {
  border-color: #9e9e9e;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 12px;
  color: #000000;
}

h3 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #444;
}

.no-input-indicator {
  color: #e74c3c;
  font-size: 11px;
  font-weight: normal;
  font-style: italic;
  margin-left: 4px;
}

@media (max-width: 768px) {
  .form-grid {
    padding: 12px;
  }
  .form-field input,
  .form-field select {
    font-size: 16px;
    padding: 10px 12px;
  }
  .no-input-indicator {
    font-size: 10px;
    margin-left: 2px;
  }
}

@media (max-width: 480px) {
  .form-field input,
  .form-field select {
    padding: 8px 10px;
    font-size: 14px;
  }
  .no-input-indicator {
    font-size: 9px;
    margin-left: 1px;
  }
}

.grouped-field-section {
  width: 100%;
  margin-bottom: 24px;
  padding: 16px;
  background-color: white;
  border-radius: 4px;
  box-sizing: border-box;
  border-left: 4px solid #007bff;
}

.grouped-field-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
}

@media (max-width: 767px) {
  .grouped-field-section {
    padding: 12px;
    margin-bottom: 16px;
  }
  .grouped-field-section h3 {
    font-size: 14px;
    margin-bottom: 12px;
  }
}

.is-multi .multi-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.is-multi .multi-input {
  flex: 1;
  min-width: 0;
}

.is-multi .multi-buttons {
  display: flex;
  flex-direction: row;
  gap: 4px;
  flex-shrink: 0;
  align-items: center;
}

.dropdown-input-container {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  min-width: 200px;
}

.dropdown-input-container .dropdown-arrow-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-left: none;
  border-radius: 0 8px 8px 0;
  background-color: #f8f9fa;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
}

.dropdown-arrow-btn:hover {
  background-color: #e9ecef;
  color: #283A97;
}

.dropdown-arrow-btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

.dropdown-arrow-btn .mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  line-height: 1;
}

.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: white;
  border: 1px solid #DBDBDB;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

.dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #495057;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #283A97;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-empty {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
}

@media (max-width: 768px) {
  .dropdown-input-container .dropdown-arrow-btn {
    width: 36px;
    height: 36px;
  }
  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
  .dropdown-item {
    padding: 10px 12px;
    font-size: 13px;
  }
  .dropdown-empty {
    padding: 10px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .dropdown-input-container .dropdown-arrow-btn {
    width: 32px;
    height: 32px;
  }
  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }
  .dropdown-item {
    padding: 8px 10px;
    font-size: 12px;
  }
  .dropdown-empty {
    padding: 8px 10px;
    font-size: 12px;
  }
} 