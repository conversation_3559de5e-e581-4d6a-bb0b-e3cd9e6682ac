<div class="form-grid" [ngClass]="'columns-' + columnCount">
  <ng-container *ngFor="let column of columns; track column">
    <div class="form-column">
      <ng-container *ngFor="let field of column; track field.fieldName">
        <!-- Skip ID field as it's handled separately -->
        @if (field.fieldName?.toUpperCase() !== 'ID') {
          <!-- Non-Grouped Fields -->
          @if (!field.Group) {
            <div class="form-field">
              <!-- Regular Field -->
              @if (!field.isMulti) {
                <label [for]="field.fieldName">{{ field.label || field.fieldName }} @if (field.mandatory) {<span>*</span>}
                  @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                </label>

                @if (field.foreginKey) {
                  <!-- Check if this is a type field (fieldType) -->
                  @if (field.foreginKey === 'fieldType') {
                    <div class="dropdown-input-container">
                      <input [formControlName]="field.fieldName" [id]="field.fieldName" type="text" 
                             class="form-input dropdown-input" 
                             (input)="typeInputChange.emit({event: $event, fieldName: field.fieldName})" 
                             (focus)="typeInputFocus.emit(field.fieldName)" 
                             (blur)="typeInputBlur.emit(field.fieldName)"
                             [disabled]="isViewMode || field.noInput"
                             [placeholder]="'Search ' + field.label?.trim() || field.fieldName" />
                      
                      <button type="button" class="dropdown-arrow-btn" 
                              (click)="toggleTypeDropdown.emit(field.fieldName)" 
                              [disabled]="isViewMode || field.noInput"
                              matTooltip="Show type suggestions">
                        <mat-icon>{{ showTypeDropdown[field.fieldName] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                      </button>
                      
                      @if (showTypeDropdown[field.fieldName]) {
                        <div class="dropdown-list">
                          @if (filteredTypeOptions[field.fieldName] && filteredTypeOptions[field.fieldName].length > 0) {
                            @for (option of filteredTypeOptions[field.fieldName]; track option.ROW_ID) {
                              <div class="dropdown-item" (click)="selectTypeOption.emit({option: option, fieldName: field.fieldName})">
                                {{ option.ROW_ID }}
                              </div>
                            }
                          } @else {
                            <div class="dropdown-empty">
                              No types found
                            </div>
                          }
                        </div>
                      }
                    </div>
                  }
                  <!-- Check if this is a foreign key field (formDefinition) -->
                  @else if (field.foreginKey === 'formDefinition') {
                    <div class="dropdown-input-container">
                      <input [formControlName]="field.fieldName" [id]="field.fieldName" type="text" 
                             class="form-input dropdown-input" 
                             (input)="foreignKeyInputChange.emit({event: $event, fieldName: field.fieldName})" 
                             (focus)="foreignKeyInputFocus.emit(field.fieldName)" 
                             (blur)="foreignKeyInputBlur.emit(field.fieldName)"
                             [disabled]="isViewMode || field.noInput"
                             [placeholder]="'Search ' + field.label?.trim() || field.fieldName" />
                      
                      <button type="button" class="dropdown-arrow-btn" 
                              (click)="toggleForeignKeyDropdown.emit(field.fieldName)" 
                              [disabled]="isViewMode || field.noInput"
                              matTooltip="Show foreign key suggestions">
                        <mat-icon>{{ showForeignKeyDropdown[field.fieldName] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                      </button>
                      
                      @if (showForeignKeyDropdown[field.fieldName]) {
                        <div class="dropdown-list">
                          @if (filteredForeignKeyOptions[field.fieldName] && filteredForeignKeyOptions[field.fieldName].length > 0) {
                            @for (option of filteredForeignKeyOptions[field.fieldName]; track option.ROW_ID) {
                              <div class="dropdown-item" (click)="selectForeignKeyOption.emit({option: option, fieldName: field.fieldName})">
                                {{ option.ROW_ID }}
                              </div>
                            }
                          } @else {
                            <div class="dropdown-empty">
                              No foreign keys found
                            </div>
                          }
                        </div>
                      }
                    </div>
                  }
                  <!-- Default dropdown for other foreign keys -->
                  @else {
                    <div class="dropdown-input-container">
                      <input [formControlName]="field.fieldName" [id]="field.fieldName" type="text" 
                             class="form-input dropdown-input" 
                             (input)="regularInputChange.emit({event: $event, fieldName: field.fieldName})" 
                             (focus)="regularInputFocus.emit(field.fieldName)" 
                             (blur)="regularInputBlur.emit(field.fieldName)"
                             [disabled]="isViewMode || field.noInput"
                             [placeholder]="'Search ' + field.label?.trim() || field.fieldName" />
                      
                      <button type="button" class="dropdown-arrow-btn" 
                              (click)="toggleRegularDropdown.emit(field.fieldName)" 
                              [disabled]="isViewMode || field.noInput"
                              matTooltip="Show options">
                        <mat-icon>{{ showRegularDropdown[field.fieldName] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                      </button>
                      
                      @if (showRegularDropdown[field.fieldName]) {
                        <div class="dropdown-list">
                          @if (filteredRegularOptions[field.fieldName] && filteredRegularOptions[field.fieldName].length > 0) {
                            @for (option of filteredRegularOptions[field.fieldName]; track option.ROW_ID) {
                              <div class="dropdown-item" (click)="selectRegularOption.emit({option: option, fieldName: field.fieldName})">
                                @for (key of getKeys(option); track key) {
                                  {{ option[key] }}&nbsp;
                                }
                              </div>
                            }
                          } @else {
                            <div class="dropdown-empty">
                              No options found
                            </div>
                          }
                        </div>
                      }
                    </div>
                  }
                } @else {
                  <!-- Regular input fields for non-foreign key fields -->
                  @if (field.type === 'string') {
                    <input [formControlName]="field.fieldName" [id]="field.fieldName" type="text" 
                           [placeholder]="field.label?.trim() || field.fieldName" 
                           [disabled]="isViewMode || field.noInput" />
                  }
                  @if (field.type === 'int') {
                    <input [formControlName]="field.fieldName" [id]="field.fieldName" type="number" 
                           [disabled]="isViewMode || field.noInput" />
                  }
                  @if (field.type === 'boolean') {
                    <input [formControlName]="field.fieldName" [id]="field.fieldName" type="checkbox" 
                           [disabled]="isViewMode || field.noInput" />
                  }
                  @if (field.type === 'date') {
                    <input [formControlName]="field.fieldName" [id]="field.fieldName" type="date" 
                           [disabled]="isViewMode || field.noInput" />
                  }
                  @if (field.type === 'double') {
                    <input [formControlName]="field.fieldName" [id]="field.fieldName" type="number" 
                           step="0.01" [disabled]="isViewMode || field.noInput" />
                  }
                }
              }

              <!-- Multi-Field -->
              @if (field.isMulti) {
                <app-multi-field
                  [field]="field"
                  [formArray]="getMultiArray(field.fieldName)"
                  [isViewMode]="isViewMode"
                  [showTypeDropdown]="showTypeDropdown"
                  [showForeignKeyDropdown]="showForeignKeyDropdown"
                  [showRegularDropdown]="showRegularDropdown"
                  [filteredTypeOptions]="filteredTypeOptions"
                  [filteredForeignKeyOptions]="filteredForeignKeyOptions"
                  [filteredRegularOptions]="filteredRegularOptions"
                  (addMultiField)="addMultiField.emit($event)"
                  (removeMultiField)="handleMultiFieldRemove($event, field.fieldName)"
                  (typeInputChange)="typeInputChange.emit($event)"
                  (typeInputFocus)="typeInputFocus.emit($event)"
                  (typeInputBlur)="typeInputBlur.emit($event)"
                  (toggleTypeDropdown)="toggleTypeDropdown.emit($event)"
                  (selectTypeOption)="selectTypeOption.emit($event)"
                  (foreignKeyInputChange)="foreignKeyInputChange.emit($event)"
                  (foreignKeyInputFocus)="foreignKeyInputFocus.emit($event)"
                  (foreignKeyInputBlur)="foreignKeyInputBlur.emit($event)"
                  (toggleForeignKeyDropdown)="toggleForeignKeyDropdown.emit($event)"
                  (selectForeignKeyOption)="selectForeignKeyOption.emit($event)"
                  (regularInputChange)="regularInputChange.emit($event)"
                  (regularInputFocus)="regularInputFocus.emit($event)"
                  (regularInputBlur)="regularInputBlur.emit($event)"
                  (toggleRegularDropdown)="toggleRegularDropdown.emit($event)"
                  (selectRegularOption)="selectRegularOption.emit($event)">
                </app-multi-field>
              }
            </div>
          }

          <!-- Grouped Fields -->
          @if (field.Group && isFirstFieldInParentGroup(field)) {
            <app-group-field
              [field]="field"
              [formArray]="getGroupArray(field.Group)"
              [isViewMode]="isViewMode"
              [isRowView]="isRowView"
              [showTypeDropdown]="showTypeDropdown"
              [showForeignKeyDropdown]="showForeignKeyDropdown"
              [showRegularDropdown]="showRegularDropdown"
              [filteredTypeOptions]="filteredTypeOptions"
              [filteredForeignKeyOptions]="filteredForeignKeyOptions"
              [filteredRegularOptions]="filteredRegularOptions"
              [getMultiArray]="getMultiArray"
              [getFieldsForGroup]="getFieldsForGroup"
              [parseGroupPath]="parseGroupPath"
              [getKeys]="getKeys"
              (addMultiField)="addMultiField.emit($event)"
              (removeMultiField)="removeMultiField.emit($event)"
              (addGroup)="addGroup.emit($event)"
              (removeGroup)="removeGroup.emit($event)"
              (cloneGroup)="cloneGroup.emit($event)"
              (typeInputChange)="typeInputChange.emit($event)"
              (typeInputFocus)="typeInputFocus.emit($event)"
              (typeInputBlur)="typeInputBlur.emit($event)"
              (toggleTypeDropdown)="toggleTypeDropdown.emit($event)"
              (selectTypeOption)="selectTypeOption.emit($event)"
              (foreignKeyInputChange)="foreignKeyInputChange.emit($event)"
              (foreignKeyInputFocus)="foreignKeyInputFocus.emit($event)"
              (foreignKeyInputBlur)="foreignKeyInputBlur.emit($event)"
              (toggleForeignKeyDropdown)="toggleForeignKeyDropdown.emit($event)"
              (selectForeignKeyOption)="selectForeignKeyOption.emit($event)"
              (regularInputChange)="regularInputChange.emit($event)"
              (regularInputFocus)="regularInputFocus.emit($event)"
              (regularInputBlur)="regularInputBlur.emit($event)"
              (toggleRegularDropdown)="toggleRegularDropdown.emit($event)"
              (selectRegularOption)="selectRegularOption.emit($event)">
            </app-group-field>
          }
        }
      </ng-container>
    </div>
  </ng-container>
</div> 