<div class="form-grid" [ngClass]="'columns-' + columnCount">
  @for (column of columns; track column) {
    <div class="form-column">
      @for (field of column; track field.fieldName) {
        <!-- Skip ID field as it's handled separately -->
        @if (field.fieldName?.toUpperCase() !== 'ID') {
          <!-- Non-Grouped Fields -->
          @if (!field.Group) {
            <div class="form-field">
              <!-- Regular Field -->
              @if (!field.isMulti) {
                <!-- Use field-renderer component for regular fields -->
                <app-field-renderer
                  [field]="field"
                  [form]="form"
                  [isViewMode]="isViewMode">
                </app-field-renderer>
              }

              <!-- Multi-Field -->
              @if (field.isMulti) {
                <app-multi-field
                  [field]="field"
                  [form]="form"
                  [isViewMode]="isViewMode"
                  (multiFieldAdded)="onMultiFieldAdded($event)"
                  (multiFieldRemoved)="onMultiFieldRemoved($event)">
                </app-multi-field>
              }
            </div>
          }

          <!-- Grouped Fields -->
          @if (field.Group && isFirstFieldInParentGroup(field)) {
            <app-group-field
              [field]="field"
              [form]="form"
              [isViewMode]="isViewMode"
              [isRowView]="isRowView"
              [fields]="fields"
              (groupAdded)="onGroupAdded($event)"
              (groupRemoved)="onGroupRemoved($event)"
              (groupCloned)="onGroupCloned($event)">
            </app-group-field>
          }
        }
      }
    </div>
  }
</div>