/* Nested Group Styling */
.nested-group-section {
  width: 100%;
  margin: 16px 0;
  padding: 16px;
  background-color: white;
  border-radius: 4px;
  box-sizing: border-box;
  border-left: 4px solid #28a745;
  margin-left: 16px;
}

.nested-group-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
}

.nested-field {
  background-color: white;
  border: 1px solid #f1f3f4;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
}

.nested-group-fields {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Nested group buttons styling */
.nested-group-buttons {
  display: flex;
  gap: 6px;
  margin-top: 12px;
  padding: 6px;
  border-top: 1px solid #f1f3f4;
  justify-content: flex-end;
  flex-wrap: wrap;
}

/* Nested group specific styling */
.nested-group-section .nested-field {
  margin-bottom: 12px;
}

.nested-group-section .form-field h5 {
  margin: 0 0 8px 0;
  font-size: 12px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.6);
}

.nested-group-section .form-field {
  margin-bottom: 8px;
}

.nested-group-section .form-field:last-child {
  margin-bottom: 0;
}

.nested-group-section .form-field label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
}

.nested-group-section .form-field input,
.nested-group-section .form-field select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 3px;
  font-size: 12px;
}

.nested-group-section .form-field input:focus,
.nested-group-section .form-field select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Multi-field styling within nested groups */
.nested-group-section .form-field.is-multi {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
}

.nested-group-section .multi-input-container {
  display: flex;
  align-items: center;
  gap: 6px;
}

.nested-group-section .multi-input {
  flex: 1;
  min-width: 0;
}

.nested-group-section .multi-buttons {
  display: flex;
  flex-direction: row;
  gap: 3px;
  flex-shrink: 0;
  align-items: center;
}

.nested-group-section .multi-input input,
.nested-group-section .multi-input select {
  width: 100%;
  padding: 4px 6px;
  border: 1px solid #ddd;
  border-radius: 3px;
  font-size: 11px;
}

/* Responsive adjustments for nested groups */
@media (max-width: 768px) {
  .nested-group-section .multi-input-container {
    flex-direction: column;
    gap: 4px;
  }
  
  .nested-group-section .multi-buttons {
    flex-direction: row;
    gap: 2px;
    align-items: center;
    justify-content: flex-end;
  }
}

@media (max-width: 480px) {
  .nested-group-section {
    padding: 12px;
    margin-left: 12px;
  }
  
  .nested-field {
    padding: 8px;
  }
  
  .nested-group-section .multi-input-container {
    flex-direction: column;
    gap: 3px;
  }
  
  .nested-group-section .multi-buttons {
    flex-direction: row;
    gap: 1px;
  }
}

/* Nested group buttons */
.nested-group-buttons {
  display: flex;
  gap: 6px;
  margin-top: 12px;
  padding: 6px;
  border-top: 1px solid #f1f3f4;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.nested-group-buttons button {
  min-width: 32px;
  height: 32px;
  padding: 0;
  font-size: 12px;
}

/* Row view nested group styling */
.row-view-nested .nested-group-buttons,
.row-view-nested-seamless .nested-group-buttons {
  display: flex;
  gap: 4px;
  margin-top: 8px;
  padding: 4px;
  border-top: 1px solid #f1f3f4;
  justify-content: flex-end;
  flex-wrap: wrap;
}

/* Row view nested group fields */
.row-view .nested-group-fields,
.row-view-nested .nested-group-fields {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
  background-color: transparent;
  border: none;
  padding: 0;
}

/* Row view nested group section */
.row-view .nested-group-section,
.row-view-nested .nested-group-section {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
  background-color: transparent;
  border: none;
  padding: 0;
  margin: 0;
  border-left: none;
}

.row-view .nested-group-section::before {
  content: '';
  width: 2px;
  height: 100%;
  background-color: #28a745;
  margin-right: 8px;
}

/* Row view nested group fields styling */
.row-view .nested-group-fields .form-field,
.row-view-nested .nested-group-fields .form-field {
  margin-bottom: 0;
  min-width: 120px;
}

.row-view .nested-group-section + .nested-group-section {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f1f3f4;
}

/* Nested group section label styling */
.nested-group-section .form-field label span {
  color: #666;
  font-size: 0.9em;
}

/* Nested group section multi-field label styling */
.nested-group-section .form-field.is-multi label {
  font-size: 11px;
  margin-bottom: 4px;
  color: rgba(0, 0, 0, 0.7);
}

/* Dropdown styling for nested groups */
.nested-group-section .dropdown-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.nested-group-section .dropdown-input-container .form-input {
  flex-grow: 1;
  border-radius: 4px 0 0 4px;
  border-right: none;
  padding: 6px 8px;
  font-size: 12px;
}

.nested-group-section .dropdown-input-container .dropdown-arrow-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #ddd;
  border-left: none;
  border-radius: 0 4px 4px 0;
  background-color: #f8f9fa;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
}

.nested-group-section .dropdown-input-container .dropdown-arrow-btn:hover {
  background-color: #e9ecef;
  color: #283A97;
}

.nested-group-section .dropdown-input-container .dropdown-arrow-btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

.nested-group-section .dropdown-input-container .dropdown-arrow-btn .mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  line-height: 1;
}

.nested-group-section .dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 32px;
  z-index: 1000;
  background-color: white;
  border: 1px solid #ddd;
  border-top: none;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-height: 150px;
  overflow-y: auto;
}

.nested-group-section .dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Poppins', sans-serif;
  font-size: 12px;
  color: #495057;
  transition: background-color 0.2s ease;
}

.nested-group-section .dropdown-item:hover {
  background-color: #f8f9fa;
  color: #283A97;
}

.nested-group-section .dropdown-item:last-child {
  border-bottom: none;
}

.nested-group-section .dropdown-empty {
  padding: 8px 12px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
  font-size: 11px;
}

/* No input indicator styling */
.no-input-indicator {
  color: #666;
  font-style: italic;
  font-size: 0.9em;
}

/* Responsive nested group styling */
@media (max-width: 768px) {
  .nested-group-section {
    margin-left: 12px;
    padding: 12px;
  }
  
  .nested-group-section .dropdown-input-container .dropdown-arrow-btn {
    width: 28px;
    height: 28px;
  }

  .nested-group-section .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 14px;
    width: 14px;
    height: 14px;
  }

  .nested-group-section .dropdown-list {
    right: 28px;
  }

  .nested-group-section .dropdown-item {
    padding: 6px 10px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .nested-group-section {
    margin-left: 8px;
    padding: 8px;
  }
  
  .nested-group-section .dropdown-input-container .dropdown-arrow-btn {
    width: 24px;
    height: 24px;
  }

  .nested-group-section .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 12px;
    width: 12px;
    height: 12px;
  }

  .nested-group-section .dropdown-list {
    right: 24px;
  }

  .nested-group-section .dropdown-item {
    padding: 4px 8px;
    font-size: 10px;
  }
} 