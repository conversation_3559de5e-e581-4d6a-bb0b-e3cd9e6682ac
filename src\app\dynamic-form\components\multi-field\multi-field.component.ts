import { Component, Input, Output, EventEmitter, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup, FormArray, FormBuilder, Validators } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FieldRendererComponent } from '../field-renderer/field-renderer.component';

@Component({
  selector: 'app-multi-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    FieldRendererComponent
  ],
  templateUrl: './multi-field.component.html',
  styleUrls: ['./multi-field.component.scss']
})
export class MultiFieldComponent {
  @Input() field: any;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() groupIndex?: number;
  @Input() groupName?: string;
  @Input() nestedGroupIndex?: number;

  @Output() multiFieldAdded = new EventEmitter<{field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number}>();
  @Output() multiFieldRemoved = new EventEmitter<{fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number}>();

  private fb = inject(FormBuilder);

  get formArray(): FormArray {
    return this.getMultiArray();
  }

  getMultiArray(): FormArray {
    if (this.groupIndex !== undefined && this.groupName) {
      // Check if this is a nested group
      const parsed = this.parseGroupPath(this.groupName);
      if (parsed.isNested && parsed.parent && parsed.child && this.nestedGroupIndex !== undefined) {
        // Navigate to nested group: parent[groupIndex].child[nestedGroupIndex].fieldName
        const parentArray = this.form.get(parsed.parent) as FormArray;
        const parentGroup = parentArray.at(this.groupIndex) as FormGroup;
        const childArray = parentGroup.get(parsed.child) as FormArray;
        const childGroup = childArray.at(this.nestedGroupIndex) as FormGroup;
        return childGroup.get(this.field.fieldName) as FormArray;
      } else {
        // Regular group
        const groupArray = this.form.get(this.groupName) as FormArray;
        const group = groupArray.at(this.groupIndex) as FormGroup;
        return group.get(this.field.fieldName) as FormArray;
      }
    } else {
      return this.form.get(this.field.fieldName) as FormArray;
    }
  }

  parseGroupPath(groupPath: string): { parent: string | null, child: string | null, isNested: boolean } {
    const trimmedGroupPath = groupPath.trim();
    if (trimmedGroupPath.includes('|')) {
      const parts = trimmedGroupPath.split('|');
      return {
        parent: parts[0].trim(),
        child: parts[1].trim(),
        isNested: true
      };
    }
    return {
      parent: trimmedGroupPath.trim(),
      child: null,
      isNested: false
    };
  }

  createMultiField(field: any): FormGroup {
    const group = this.fb.group({});
    if (Array.isArray(field)) {
      field.forEach(fieldName => {
        const control = this.fb.control('', Validators.required);
        group.addControl(fieldName, control);
      });
    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
    return group;
  }

  addMultiField(index?: number): void {
    try {
      const multiArray = this.getMultiArray();
      const newField = this.createMultiField(this.field);
      if (index !== undefined) {
        multiArray.insert(index + 1, newField);
      } else {
        multiArray.push(newField);
      }

      // Emit event for parent component
      this.multiFieldAdded.emit({
        field: this.field,
        groupIndex: this.groupIndex,
        index: index,
        groupName: this.groupName,
        nestedGroupIndex: this.nestedGroupIndex
      });
    } catch (error) {
      // Handle error silently
    }
  }

  removeMultiField(index: number): void {
    const multiArray = this.getMultiArray();
    multiArray.removeAt(index);

    // Emit event for parent component
    this.multiFieldRemoved.emit({
      fieldName: this.field.fieldName,
      index: index,
      groupIndex: this.groupIndex,
      groupName: this.groupName,
      nestedGroupIndex: this.nestedGroupIndex
    });
  }

  canRemove(): boolean {
    return this.formArray.length > 1 && !this.isViewMode && !this.field.noInput;
  }

  canAdd(): boolean {
    return !this.isViewMode && !this.field.noInput;
  }
}