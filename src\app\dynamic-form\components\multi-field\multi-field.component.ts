import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DropdownFieldComponent } from '../dropdown-field/dropdown-field.component';

@Component({
  selector: 'app-multi-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule
  ],
  templateUrl: './multi-field.component.html',
  styleUrls: ['./multi-field.component.scss']
})
export class MultiFieldComponent {
  @Input() field: any;
  @Input() formArray!: FormArray;
  @Input() isViewMode: boolean = false;
  @Input() showTypeDropdown: { [key: string]: boolean } = {};
  @Input() showForeignKeyDropdown: { [key: string]: boolean } = {};
  @Input() showRegularDropdown: { [key: string]: boolean } = {};
  @Input() filteredTypeOptions: { [key: string]: any[] } = {};
  @Input() filteredForeignKeyOptions: { [key: string]: any[] } = {};
  @Input() filteredRegularOptions: { [key: string]: any[] } = {};

  @Output() addMultiField = new EventEmitter<{field: any, index: number}>();
  @Output() removeMultiField = new EventEmitter<number>();
  @Output() typeInputChange = new EventEmitter<{event: Event, fieldName: string}>();
  @Output() typeInputFocus = new EventEmitter<string>();
  @Output() typeInputBlur = new EventEmitter<string>();
  @Output() toggleTypeDropdown = new EventEmitter<string>();
  @Output() selectTypeOption = new EventEmitter<{option: any, fieldName: string}>();
  @Output() foreignKeyInputChange = new EventEmitter<{event: Event, fieldName: string}>();
  @Output() foreignKeyInputFocus = new EventEmitter<string>();
  @Output() foreignKeyInputBlur = new EventEmitter<string>();
  @Output() toggleForeignKeyDropdown = new EventEmitter<string>();
  @Output() selectForeignKeyOption = new EventEmitter<{option: any, fieldName: string}>();
  @Output() regularInputChange = new EventEmitter<{event: Event, fieldName: string}>();
  @Output() regularInputFocus = new EventEmitter<string>();
  @Output() regularInputBlur = new EventEmitter<string>();
  @Output() toggleRegularDropdown = new EventEmitter<string>();
  @Output() selectRegularOption = new EventEmitter<{option: any, fieldName: string}>();

  getFieldName(j: number): string {
    return `${this.field.fieldName}_${j}`;
  }

  getKeys(option: any): string[] {
    return Object.keys(option);
  }
} 