/* حاوية الحقل المتعدد */
.is-multi .multi-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

/* الحقل نفسه */
.is-multi .multi-input {
  flex: 1;
  min-width: 0; /* Allow flex item to shrink */
}

/* الأزرار */
.is-multi .multi-buttons {
  display: flex;
  flex-direction: row;
  gap: 4px;
  flex-shrink: 0;
  align-items: center;
}

/* Angular Material button overrides for better responsiveness and centering */
.mat-mdc-icon-button {
  width: 36px !important;
  height: 36px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.mat-mdc-icon-button .mat-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
  padding: 0 !important;
  line-height: 1 !important;
}

.mat-mdc-raised-button {
  min-width: auto !important;
  padding: 8px 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
}

.mat-mdc-raised-button .mat-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
  padding: 0 !important;
  line-height: 1 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .multi-buttons {
    flex-direction: column;
    gap: 2px;
  }

  .mat-mdc-icon-button {
    width: 32px !important;
    height: 32px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .mat-mdc-icon-button .mat-icon {
    font-size: 18px !important;
    width: 18px !important;
    height: 18px !important;
  }

  .mat-mdc-raised-button {
    padding: 6px 12px !important;
    font-size: 12px !important;
  }

  .mat-mdc-raised-button .mat-icon {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
  }
}

@media (max-width: 480px) {
  .mat-mdc-icon-button {
    width: 28px !important;
    height: 28px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .mat-mdc-icon-button .mat-icon {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
  }

  .mat-mdc-raised-button {
    padding: 4px 8px !important;
    font-size: 11px !important;
  }

  .mat-mdc-raised-button .mat-icon {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
  }
}

/* Additional icon centering fixes */
.mat-mdc-button .mat-icon,
.mat-mdc-raised-button .mat-icon,
.mat-mdc-icon-button .mat-icon {
  vertical-align: middle !important;
  text-align: center !important;
}

/* Fix Material Icons font rendering issues */
.mat-icon {
  font-family: 'Material Icons' !important;
  font-weight: normal !important;
  font-style: normal !important;
  font-size: 24px !important;
  line-height: 1 !important;
  letter-spacing: normal !important;
  text-transform: none !important;
  display: inline-block !important;
  white-space: nowrap !important;
  word-wrap: normal !important;
  direction: ltr !important;
  -webkit-font-feature-settings: 'liga' !important;
  -webkit-font-smoothing: antialiased !important;
  text-rendering: optimizeLegibility !important;
  -moz-osx-font-smoothing: grayscale !important;
  font-feature-settings: 'liga' !important;
}

/* Ensure icons are properly sized and centered */
.mat-mdc-icon-button .mat-icon {
  font-size: 20px !important;
  width: 20px !important;
  height: 20px !important;
  line-height: 20px !important;
}

/* Force icon visibility and prevent layout shifts */
.mat-icon.material-icons {
  visibility: visible !important;
  opacity: 1 !important;
  transform: none !important;
}

/* Prevent icon flickering during load */
.mat-icon::before {
  content: '' !important;
}

/* Ensure proper icon loading state */
.mat-icon:not(.mat-icon-inline) {
  min-width: 24px !important;
  min-height: 24px !important;
}

/* Prevent icon content from changing after click */
.mat-icon {
  /* Force consistent rendering */
  transform: none !important;
  transition: none !important;
}

/* Ensure proper button states */
.mat-mdc-icon-button:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

.mat-mdc-icon-button:not(:disabled):hover {
  background-color: rgba(0, 0, 0, 0.04) !important;
}

.mat-mdc-icon-button:not(:disabled):active {
  background-color: rgba(0, 0, 0, 0.08) !important;
}

/* Multi-field specific styling */
.form-field.is-multi {
  margin-bottom: 16px;
  background: #f9f9f9;
  border-radius: 6px;
  padding: 16px 12px 12px 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}

.form-field.is-multi label {
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.no-input-indicator {
  color: #666;
  font-style: italic;
  font-size: 0.9em;
}

/* Dropdown styling for multi-fields */
.multi-input .dropdown-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.multi-input .dropdown-input-container .form-input {
  flex-grow: 1;
  border-radius: 8px 0 0 8px;
  border-right: none;
  padding: 8px 12px;
}

.multi-input .dropdown-input-container .dropdown-arrow-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-left: none;
  border-radius: 0 8px 8px 0;
  background-color: #f8f9fa;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
}

.multi-input .dropdown-input-container .dropdown-arrow-btn:hover {
  background-color: #e9ecef;
  color: #283A97;
}

.multi-input .dropdown-input-container .dropdown-arrow-btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

.multi-input .dropdown-input-container .dropdown-arrow-btn .mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  line-height: 1;
}

.multi-input .dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 40px;
  z-index: 1000;
  background-color: white;
  border: 1px solid #DBDBDB;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

.multi-input .dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #495057;
  transition: background-color 0.2s ease;
}

.multi-input .dropdown-item:hover {
  background-color: #f8f9fa;
  color: #283A97;
}

.multi-input .dropdown-item:last-child {
  border-bottom: none;
}

.multi-input .dropdown-empty {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
}

/* Responsive multi-field styling */
@media (max-width: 768px) {
  .form-field.is-multi {
    padding: 10px 6px 6px 6px;
  }
  
  .multi-input-container {
    flex-direction: column;
    gap: 8px;
  }
  
  .multi-buttons {
    flex-direction: row;
    gap: 8px;
    align-items: center;
    justify-content: flex-end;
  }
  
  .multi-input .dropdown-input-container .dropdown-arrow-btn {
    width: 36px;
    height: 36px;
  }

  .multi-input .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }

  .multi-input .dropdown-list {
    right: 36px;
  }

  .multi-input .dropdown-item {
    padding: 10px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .multi-input .dropdown-input-container .dropdown-arrow-btn {
    width: 32px;
    height: 32px;
  }

  .multi-input .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }

  .multi-input .dropdown-list {
    right: 32px;
  }

  .multi-input .dropdown-item {
    padding: 8px 10px;
    font-size: 12px;
  }
} 