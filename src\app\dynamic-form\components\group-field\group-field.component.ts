import { Component, Input, Output, EventEmitter, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup, FormArray, FormBuilder } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FieldRendererComponent } from '../field-renderer/field-renderer.component';

@Component({
  selector: 'app-group-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    FieldRendererComponent
  ],
  templateUrl: './group-field.component.html',
  styleUrls: ['./group-field.component.scss']
})
export class GroupFieldComponent {
  @Input() field: any;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() isRowView: boolean = false;
  @Input() fields: any[] = [];

  @Output() groupAdded = new EventEmitter<{groupName: string, index: number}>();
  @Output() groupRemoved = new EventEmitter<{groupName: string, index: number}>();
  @Output() groupCloned = new EventEmitter<{groupName: string, index: number}>();

  private fb = inject(FormBuilder);

  get parsedGroup() {
    return this.parseGroupPath(this.field.Group);
  }

  get groupArray(): FormArray {
    return this.getGroupArray(this.parsedGroup.parent!);
  }

  parseGroupPath(groupPath: string): { parent: string | null, child: string | null, isNested: boolean } {
    const trimmedGroupPath = groupPath.trim();
    if (trimmedGroupPath.includes('|')) {
      const parts = trimmedGroupPath.split('|');
      return {
        parent: parts[0].trim(),
        child: parts[1].trim(),
        isNested: true
      };
    }
    return {
      parent: trimmedGroupPath.trim(),
      child: null,
      isNested: false
    };
  }

  getGroupArray(groupName: string): FormArray {
    return this.form.get(groupName) as FormArray;
  }

  getFieldsForGroup(groupName: string): any[] {
    return this.fields.filter(field => {
      if (!field.Group) return false;
      const parsed = this.parseGroupPath(field.Group);
      return parsed.parent === groupName;
    });
  }

  addGroup(groupName: string, index: number): void {
    const groupArray = this.getGroupArray(groupName);
    const newGroup = this.createGroup(groupName);
    groupArray.insert(index + 1, newGroup);

    this.groupAdded.emit({ groupName, index });
  }

  removeGroup(groupName: string, index: number): void {
    const groupArray = this.getGroupArray(groupName);
    groupArray.removeAt(index);

    this.groupRemoved.emit({ groupName, index });
  }

  cloneGroup(groupName: string, index: number): void {
    const groupArray = this.getGroupArray(groupName);
    const sourceGroup = groupArray.at(index) as FormGroup;
    const clonedGroup = this.createGroup(groupName);

    // Copy values from source group
    clonedGroup.patchValue(sourceGroup.value);
    groupArray.insert(index + 1, clonedGroup);

    this.groupCloned.emit({ groupName, index });
  }

  createGroup(groupName: string): FormGroup {
    const group = this.fb.group({});
    const groupFields = this.getFieldsForGroup(groupName);

    groupFields.forEach(field => {
      this.addFieldToGroup(group, field);
    });

    return group;
  }

  private addFieldToGroup(group: FormGroup, field: any): void {
    if (field.isMulti) {
      const multiFieldArray = this.fb.array([this.createMultiField(field)]);
      group.addControl(field.fieldName, multiFieldArray);

      // Disable multi-field if noInput is true
      if (field.noInput) {
        multiFieldArray.disable({ emitEvent: false });
      }
    } else {
      const control = this.fb.control("", field.mandatory ? [] : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
  }

  private createMultiField(field: any): FormGroup {
    const group = this.fb.group({});
    const control = this.fb.control("", field.mandatory ? [] : null);
    group.addControl(field.fieldName, control);

    // Disable control if noInput is true
    if (field.noInput) {
      control.disable({ emitEvent: false });
    }

    return group;
  }

  canRemoveGroup(): boolean {
    return this.groupArray.length > 1 && !this.isViewMode;
  }

  canAddGroup(): boolean {
    return !this.isViewMode;
  }

  canCloneGroup(): boolean {
    return !this.isViewMode;
  }
}