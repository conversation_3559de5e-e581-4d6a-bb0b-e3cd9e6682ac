import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormGroup, ReactiveFormsModule, AbstractControl } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MultiFieldComponent } from '../multi-field/multi-field.component';
import { FieldRendererComponent } from '../field-renderer/field-renderer.component';
import { NestedGroupComponent } from '../nested-group/nested-group.component';

@Component({
  selector: 'app-group-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule
  ],
  templateUrl: './group-field.component.html',
  styleUrls: ['./group-field.component.scss']
})
export class GroupFieldComponent {
  @Input() field: any;
  @Input() formArray!: FormArray;
  @Input() isViewMode: boolean = false;
  @Input() isRowView: boolean = false;
  @Input() getFieldsForGroup!: (groupName: string) => any[];
  @Input() getFieldsForGroupPath!: (groupPath: string) => any[];
  @Input() getChildGroups!: (parentGroup: string) => string[];
  @Input() getNestedGroupArray!: (groupPath: string, parentIndex?: number) => FormArray;
  @Input() getMultiArray!: (fieldName: string, groupIndex?: number, groupName?: string, nestedGroupIndex?: number) => FormArray;
  @Input() parseGroupPath!: (groupPath: string) => { parent: string | null, child: string | null, isNested: boolean };
  @Input() showTypeDropdown: { [key: string]: boolean } = {};
  @Input() showForeignKeyDropdown: { [key: string]: boolean } = {};
  @Input() showRegularDropdown: { [key: string]: boolean } = {};
  @Input() filteredTypeOptions: { [key: string]: any[] } = {};
  @Input() filteredForeignKeyOptions: { [key: string]: any[] } = {};
  @Input() filteredRegularOptions: { [key: string]: any[] } = {};
  @Input() getKeys!: (option: any) => string[];

  @Output() addGroup = new EventEmitter<{groupName: string, index: number}>();
  @Output() removeGroup = new EventEmitter<{groupName: string, index: number}>();
  @Output() cloneGroup = new EventEmitter<{groupName: string, index: number}>();
  @Output() addMultiField = new EventEmitter<{field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number}>();
  @Output() removeMultiField = new EventEmitter<{fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number}>();
  @Output() typeInputChange = new EventEmitter<{event: Event, fieldName: string}>();
  @Output() typeInputFocus = new EventEmitter<string>();
  @Output() typeInputBlur = new EventEmitter<string>();
  @Output() toggleTypeDropdown = new EventEmitter<string>();
  @Output() selectTypeOption = new EventEmitter<{option: any, fieldName: string}>();
  @Output() foreignKeyInputChange = new EventEmitter<{event: Event, fieldName: string}>();
  @Output() foreignKeyInputFocus = new EventEmitter<string>();
  @Output() foreignKeyInputBlur = new EventEmitter<string>();
  @Output() toggleForeignKeyDropdown = new EventEmitter<string>();
  @Output() selectForeignKeyOption = new EventEmitter<{option: any, fieldName: string}>();
  @Output() regularInputChange = new EventEmitter<{event: Event, fieldName: string}>();
  @Output() regularInputFocus = new EventEmitter<string>();
  @Output() regularInputBlur = new EventEmitter<string>();
  @Output() toggleRegularDropdown = new EventEmitter<string>();
  @Output() selectRegularOption = new EventEmitter<{option: any, fieldName: string}>();

  getGroupName(): string {
    return this.field.Group;
  }

  getFormGroup(control: AbstractControl): FormGroup {
    return control as FormGroup;
  }

  trackByIndex(index: number, _item: any): number {
    return index;
  }

  onAddGroup(index: number): void {
    this.addGroup.emit({ groupName: this.getGroupName(), index });
  }

  onRemoveGroup(index: number): void {
    this.removeGroup.emit({ groupName: this.getGroupName(), index });
  }

  onCloneGroup(index: number): void {
    this.cloneGroup.emit({ groupName: this.getGroupName(), index });
  }

  onAddMultiField(field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number): void {
    this.addMultiField.emit({ field, groupIndex, index, groupName, nestedGroupIndex });
  }

  onRemoveMultiField(fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number): void {
    this.removeMultiField.emit({ fieldName, index, groupIndex, groupName, nestedGroupIndex });
  }

  onTypeInputChange(event: Event, fieldName: string): void {
    this.typeInputChange.emit({ event, fieldName });
  }

  onTypeInputFocus(fieldName: string): void {
    this.typeInputFocus.emit(fieldName);
  }

  onTypeInputBlur(fieldName: string): void {
    this.typeInputBlur.emit(fieldName);
  }

  onToggleTypeDropdown(fieldName: string): void {
    this.toggleTypeDropdown.emit(fieldName);
  }

  onSelectTypeOption(option: any, fieldName: string): void {
    this.selectTypeOption.emit({ option, fieldName });
  }

  onForeignKeyInputChange(event: Event, fieldName: string): void {
    this.foreignKeyInputChange.emit({ event, fieldName });
  }

  onForeignKeyInputFocus(fieldName: string): void {
    this.foreignKeyInputFocus.emit(fieldName);
  }

  onForeignKeyInputBlur(fieldName: string): void {
    this.foreignKeyInputBlur.emit(fieldName);
  }

  onToggleForeignKeyDropdown(fieldName: string): void {
    this.toggleForeignKeyDropdown.emit(fieldName);
  }

  onSelectForeignKeyOption(option: any, fieldName: string): void {
    this.selectForeignKeyOption.emit({ option, fieldName });
  }

  onRegularInputChange(event: Event, fieldName: string): void {
    this.regularInputChange.emit({ event, fieldName });
  }

  onRegularInputFocus(fieldName: string): void {
    this.regularInputFocus.emit(fieldName);
  }

  onRegularInputBlur(fieldName: string): void {
    this.regularInputBlur.emit(fieldName);
  }

  onToggleRegularDropdown(fieldName: string): void {
    this.toggleRegularDropdown.emit(fieldName);
  }

  onSelectRegularOption(option: any, fieldName: string): void {
    this.selectRegularOption.emit({ option, fieldName });
  }

  // Helper methods to match old dynamic-form behavior
  isFirstFieldInParentGroup(field: any): boolean {
    if (!field.Group) return false;
    const parsed = this.parseGroupPath(field.Group);
    if (!parsed.parent) return false;
    return true; // This will be handled by the parent component
  }

  getGroupArray(groupName: string): FormArray {
    return this.formArray;
  }

  // Method to check if group should be disabled (only one group instance)
  isGroupDisabled(index: number): boolean {
    return this.formArray.length <= 1;
  }

  // Method to get the number of multi-field items for a specific field
  getMultiFieldCount(fieldName: string, groupIndex: number): number {
    try {
      const multiArray = this.getMultiArray(fieldName, groupIndex, this.getGroupName());
      return multiArray.length;
    } catch (error) {
      return 0;
    }
  }

  // Method to check if multi-field should be disabled (only one item)
  isMultiFieldDisabled(fieldName: string, groupIndex: number): boolean {
    return this.getMultiFieldCount(fieldName, groupIndex) <= 1;
  }
} 