import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AbstractControl } from '@angular/forms';

@Component({
  selector: 'app-error-message',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './error-message.component.html',
  styleUrls: ['./error-message.component.scss']
})
export class ErrorMessageComponent {
  @Input() control?: AbstractControl;
  @Input() fieldName: string = '';
  @Input() errorMessage: string = '';

  getErrorMessage(): string {
    // If a direct error message is provided, use it (matches old dynamic-form behavior)
    if (this.errorMessage && this.errorMessage.length > 0) {
      return this.errorMessage;
    }
    
    // For field-level validation (if control is provided)
    if (this.control?.errors) {
      const errors = this.control.errors;
      
      if (errors['required']) {
        return `${this.fieldName} is required`;
      }
      if (errors['email']) {
        return `${this.fieldName} must be a valid email`;
      }
      if (errors['minlength']) {
        return `${this.fieldName} must be at least ${errors['minlength'].requiredLength} characters`;
      }
      if (errors['maxlength']) {
        return `${this.fieldName} must be at most ${errors['maxlength'].requiredLength} characters`;
      }
      if (errors['pattern']) {
        return `${this.fieldName} format is invalid`;
      }
      if (errors['min']) {
        return `${this.fieldName} must be at least ${errors['min'].min}`;
      }
      if (errors['max']) {
        return `${this.fieldName} must be at most ${errors['max'].max}`;
      }
      
      return `${this.fieldName} is invalid`;
    }
    
    return '';
  }

  hasError(): boolean {
    // Show error if there's a direct error message (matches old dynamic-form behavior)
    if (this.errorMessage && this.errorMessage.length > 0) {
      return true;
    }
    
    // For field-level validation (if control is provided)
    if (this.control) {
      return this.control.invalid && (this.control.dirty || this.control.touched) || false;
    }
    
    return false;
  }
} 