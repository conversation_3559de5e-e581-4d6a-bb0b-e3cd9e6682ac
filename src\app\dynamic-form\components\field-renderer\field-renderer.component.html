<label [for]="getFieldName()">{{ field.label || field.fieldName }} @if (field.mandatory) {<span>*</span>}
  @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
</label>

@if (field.foreginKey) {
  <!-- Check if this is a type field (fieldType) -->
  @if (isTypeField()) {
    <div class="dropdown-input-container">
      <input [formControlName]="getFieldName()" [id]="getFieldName()" type="text" 
             class="form-input dropdown-input" 
             (input)="onTypeInputChange($event)" 
             (focus)="onTypeInputFocus()" 
             (blur)="onTypeInputBlur()"
             [disabled]="isViewMode || field.noInput"
             [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
      
      <!-- Arrow button to toggle dropdown -->
      <button type="button" class="dropdown-arrow-btn" 
              (click)="onToggleTypeDropdown()" 
              [disabled]="isViewMode || field.noInput"
              matTooltip="Show type suggestions">
        <mat-icon>{{ showTypeDropdown[getFieldName()] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
      </button>
      
      <!-- Dropdown list for filtered results -->
      @if (showTypeDropdown[getFieldName()]) {
        <div class="dropdown-list">
          @if (filteredTypeOptions[getFieldName()] && filteredTypeOptions[getFieldName()].length > 0) {
            @for (option of filteredTypeOptions[getFieldName()]; track option.ROW_ID) {
              <div class="dropdown-item" (click)="onSelectTypeOption(option)">
                {{ option.ROW_ID }}
              </div>
            }
          } @else {
            <div class="dropdown-empty">
              No types found
            </div>
          }
        </div>
      }
    </div>
  }
  <!-- Check if this is a foreign key field (formDefinition) -->
  @else if (isForeignKeyField()) {
    <div class="dropdown-input-container">
      <input [formControlName]="getFieldName()" [id]="getFieldName()" type="text" 
             class="form-input dropdown-input" 
             (input)="onForeignKeyInputChange($event)" 
             (focus)="onForeignKeyInputFocus()" 
             (blur)="onForeignKeyInputBlur()"
             [disabled]="isViewMode || field.noInput"
             [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
      
      <!-- Arrow button to toggle dropdown -->
      <button type="button" class="dropdown-arrow-btn" 
              (click)="onToggleForeignKeyDropdown()" 
              [disabled]="isViewMode || field.noInput"
              matTooltip="Show foreign key suggestions">
        <mat-icon>{{ showForeignKeyDropdown[getFieldName()] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
      </button>
      
      <!-- Dropdown list for filtered results -->
      @if (showForeignKeyDropdown[getFieldName()]) {
        <div class="dropdown-list">
          @if (filteredForeignKeyOptions[getFieldName()] && filteredForeignKeyOptions[getFieldName()].length > 0) {
            @for (option of filteredForeignKeyOptions[getFieldName()]; track option.ROW_ID) {
              <div class="dropdown-item" (click)="onSelectForeignKeyOption(option)">
                {{ option.ROW_ID }}
              </div>
            }
          } @else {
            <div class="dropdown-empty">
              No foreign keys found
            </div>
          }
        </div>
      }
    </div>
  }
  <!-- Default dropdown for other foreign keys -->
  @else {
    <div class="dropdown-input-container">
      <input [formControlName]="getFieldName()" [id]="getFieldName()" type="text" 
             class="form-input dropdown-input" 
             (input)="onRegularInputChange($event)" 
             (focus)="onRegularInputFocus()" 
             (blur)="onRegularInputBlur()"
             [disabled]="isViewMode || field.noInput"
             [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
      
      <!-- Arrow button to toggle dropdown -->
      <button type="button" class="dropdown-arrow-btn" 
              (click)="onToggleRegularDropdown()" 
              [disabled]="isViewMode || field.noInput"
              matTooltip="Show options">
        <mat-icon>{{ showRegularDropdown[getFieldName()] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
      </button>
      
      <!-- Dropdown list for filtered results -->
      @if (showRegularDropdown[getFieldName()]) {
        <div class="dropdown-list">
          @if (filteredRegularOptions[getFieldName()] && filteredRegularOptions[getFieldName()].length > 0) {
            @for (option of filteredRegularOptions[getFieldName()]; track option.ROW_ID) {
              <div class="dropdown-item" (click)="onSelectRegularOption(option)">
                @for (key of getKeys(option); track key) {
                  {{ option[key] }}&nbsp;
                }
              </div>
            }
          } @else {
            <div class="dropdown-empty">
              No options found
            </div>
          }
        </div>
      }
    </div>
  }
} @else {
  <!-- Regular input fields for non-foreign key fields -->
  @if (field.type === 'boolean') {
    <input [formControlName]="getFieldName()" [id]="getFieldName()"
      type="checkbox" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
  }
  @if (field.type === 'string') {
    <input [formControlName]="getFieldName()" [id]="getFieldName()"
      type="text" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" [placeholder]="(field.label?.trim() || field.fieldName)" />
  }
  @if (field.type === 'int') {
    <input [formControlName]="getFieldName()" [id]="getFieldName()"
      type="number" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
  }
  @if (field.type === 'date') {
    <input [formControlName]="getFieldName()" [id]="getFieldName()"
      type="date" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
  }
  @if (field.type === 'double') {
    <input [formControlName]="getFieldName()" [id]="getFieldName()"
      type="number" step="00.50" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
  }
} 