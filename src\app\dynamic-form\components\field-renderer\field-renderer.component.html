<!-- Field Renderer Component Template -->
<label [for]="getFieldName()">{{ field.label || field.fieldName }} @if (field.mandatory) {<span>*</span>}
  @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
</label>

@if (isDropdownField()) {
  <!-- Use dropdown-field component for all dropdown types -->
  <app-dropdown-field
    [field]="field"
    [form]="form"
    [isViewMode]="isViewMode"
    [dropdownType]="getDropdownType()"
    [fieldSuffix]="fieldSuffix"
    (optionSelected)="onDropdownOptionSelected($event)">
  </app-dropdown-field>
} @else {
  <!-- Regular input fields for non-foreign key fields -->
  @if (field.type === 'boolean') {
    <input [formControlName]="getFieldName()" [id]="getFieldName()"
      type="checkbox" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
  }
  @if (field.type === 'string') {
    <input [formControlName]="getFieldName()" [id]="getFieldName()"
      type="text" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" [placeholder]="getPlaceholder()" />
  }
  @if (field.type === 'int') {
    <input [formControlName]="getFieldName()" [id]="getFieldName()"
      type="number" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
  }
  @if (field.type === 'date') {
    <input [formControlName]="getFieldName()" [id]="getFieldName()"
      type="date" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
  }
  @if (field.type === 'double') {
    <input [formControlName]="getFieldName()" [id]="getFieldName()"
      type="number" step="00.50" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
  }

  <!-- 🔧 FALLBACK: Handle fields without explicit type or unrecognized types -->
  @if (!field.type || (field.type !== 'boolean' && field.type !== 'string' && field.type !== 'int' && field.type !== 'date' && field.type !== 'double')) {
    <input [formControlName]="getFieldName()" [id]="getFieldName()"
      type="text" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" [placeholder]="getPlaceholder()" />
  }
}