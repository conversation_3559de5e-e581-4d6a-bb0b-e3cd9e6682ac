import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MultiFieldComponent } from '../multi-field/multi-field.component';
import { FieldRendererComponent } from '../field-renderer/field-renderer.component';
import { ErrorMessageComponent } from '../error-message/error-message.component';

@Component({
  selector: 'app-nested-group',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule
  ],
  templateUrl: './nested-group.component.html',
  styleUrls: ['./nested-group.component.scss']
})
export class NestedGroupComponent {
  @Input() nestedGroupFields: any[] = [];
  @Input() formGroup!: FormGroup;
  @Input() groupIndex: number = 0;
  @Input() parentGroupName: string = '';
  @Input() isViewMode: boolean = false;
  @Input() showTypeDropdown: { [key: string]: boolean } = {};
  @Input() showForeignKeyDropdown: { [key: string]: boolean } = {};
  @Input() showRegularDropdown: { [key: string]: boolean } = {};
  @Input() filteredTypeOptions: { [key: string]: any[] } = {};
  @Input() filteredForeignKeyOptions: { [key: string]: any[] } = {};
  @Input() filteredRegularOptions: { [key: string]: any[] } = {};
  @Input() getMultiArray!: (fieldName: string, groupIndex?: number, groupName?: string, nestedGroupIndex?: number) => any;

  @Output() addMultiField = new EventEmitter<{field: any, index: number, groupIndex: number, parentGroupName: string}>();
  @Output() removeMultiField = new EventEmitter<{fieldName: string, index: number, groupIndex: number, parentGroupName: string}>();
  @Output() typeInputChange = new EventEmitter<{event: Event, fieldName: string}>();
  @Output() typeInputFocus = new EventEmitter<string>();
  @Output() typeInputBlur = new EventEmitter<string>();
  @Output() toggleTypeDropdown = new EventEmitter<string>();
  @Output() selectTypeOption = new EventEmitter<{option: any, fieldName: string}>();
  @Output() foreignKeyInputChange = new EventEmitter<{event: Event, fieldName: string}>();
  @Output() foreignKeyInputFocus = new EventEmitter<string>();
  @Output() foreignKeyInputBlur = new EventEmitter<string>();
  @Output() toggleForeignKeyDropdown = new EventEmitter<string>();
  @Output() selectForeignKeyOption = new EventEmitter<{option: any, fieldName: string}>();
  @Output() regularInputChange = new EventEmitter<{event: Event, fieldName: string}>();
  @Output() regularInputFocus = new EventEmitter<string>();
  @Output() regularInputBlur = new EventEmitter<string>();
  @Output() toggleRegularDropdown = new EventEmitter<string>();
  @Output() selectRegularOption = new EventEmitter<{option: any, fieldName: string}>();

  getFieldName(fieldName: string): string {
    return `${fieldName}_nested_${this.groupIndex}_${this.parentGroupName}`;
  }

  getMultiFieldName(fieldName: string, index: number): string {
    return `${fieldName}_nested_${this.groupIndex}_${this.parentGroupName}_multi_${index}`;
  }

  getKeys(option: any): string[] {
    return Object.keys(option);
  }
} 