import { Component, Input, Output, EventEmitter, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup, FormArray, FormBuilder } from '@angular/forms';
import { FieldRendererComponent } from '../field-renderer/field-renderer.component';
import { MultiFieldComponent } from '../multi-field/multi-field.component';

@Component({
  selector: 'app-nested-group',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FieldRendererComponent,
    MultiFieldComponent
  ],
  templateUrl: './nested-group.component.html',
  styleUrls: ['./nested-group.component.scss']
})
export class NestedGroupComponent {
  @Input() nestedGroupFields: any[] = [];
  @Input() formGroup!: FormGroup;
  @Input() groupIndex: number = 0;
  @Input() parentGroupName: string = '';
  @Input() isViewMode: boolean = false;

  @Output() multiFieldAdded = new EventEmitter<{field: any, index: number, groupIndex: number, parentGroupName: string}>();
  @Output() multiFieldRemoved = new EventEmitter<{fieldName: string, index: number, groupIndex: number, parentGroupName: string}>();

  private fb = inject(FormBuilder);

  /**
   * Parse nested group path with pipe notation (e.g., "parent|child")
   */
  parseNestedGroupPath(groupPath: string): { parent: string | null, child: string | null, isNested: boolean } {
    const trimmedGroupPath = groupPath.trim();
    if (trimmedGroupPath.includes('|')) {
      const parts = trimmedGroupPath.split('|');
      return {
        parent: parts[0].trim(),
        child: parts[1].trim(),
        isNested: true
      };
    }
    return {
      parent: trimmedGroupPath.trim(),
      child: null,
      isNested: false
    };
  }

  /**
   * Get nested group form array for complex hierarchy handling
   */
  getNestedGroupArray(groupPath: string, parentIndex?: number): FormArray {
    const parsed = this.parseNestedGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child) {
      const parentArray = this.formGroup.get(parsed.parent) as FormArray;
      if (parentArray && parentIndex !== undefined) {
        const parentGroup = parentArray.at(parentIndex) as FormGroup;
        return parentGroup.get(parsed.child) as FormArray;
      }
    }
    return this.formGroup.get(groupPath) as FormArray;
  }

  /**
   * Create nested group with proper form controls
   */
  createNestedGroup(groupName: string): FormGroup {
    const group = this.fb.group({});
    const groupFields = this.getFieldsForNestedGroup(groupName);

    groupFields.forEach(field => {
      this.addFieldToNestedGroup(group, field);
    });

    return group;
  }

  private getFieldsForNestedGroup(groupName: string): any[] {
    return this.nestedGroupFields.filter(field => {
      if (!field.Group) return false;
      const parsed = this.parseNestedGroupPath(field.Group);
      return parsed.child === groupName;
    });
  }

  private addFieldToNestedGroup(group: FormGroup, field: any): void {
    if (field.isMulti) {
      const multiFieldArray = this.fb.array([this.createMultiField(field)]);
      group.addControl(field.fieldName, multiFieldArray);

      // Disable multi-field if noInput is true
      if (field.noInput) {
        multiFieldArray.disable({ emitEvent: false });
      }
    } else {
      const control = this.fb.control("", field.mandatory ? [] : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
  }

  private createMultiField(field: any): FormGroup {
    const group = this.fb.group({});
    const control = this.fb.control("", field.mandatory ? [] : null);
    group.addControl(field.fieldName, control);

    // Disable control if noInput is true
    if (field.noInput) {
      control.disable({ emitEvent: false });
    }

    return group;
  }

  getFieldName(fieldName: string): string {
    return `${fieldName}_nested_${this.groupIndex}_${this.parentGroupName}`;
  }

  onMultiFieldAdded(event: any): void {
    this.multiFieldAdded.emit({
      field: event.field,
      index: event.index,
      groupIndex: this.groupIndex,
      parentGroupName: this.parentGroupName
    });
  }

  onMultiFieldRemoved(event: any): void {
    this.multiFieldRemoved.emit({
      fieldName: event.fieldName,
      index: event.index,
      groupIndex: this.groupIndex,
      parentGroupName: this.parentGroupName
    });
  }

  /**
   * Static method to parse nested group path with pipe notation
   */
  static parseNestedGroupPath(groupPath: string): { parent: string | null, child: string | null, isNested: boolean } {
    const trimmedGroupPath = groupPath.trim();
    if (trimmedGroupPath.includes('|')) {
      const parts = trimmedGroupPath.split('|');
      return {
        parent: parts[0].trim(),
        child: parts[1].trim(),
        isNested: true
      };
    }
    return {
      parent: trimmedGroupPath.trim(),
      child: null,
      isNested: false
    };
  }

  /**
   * Static method to get fields for a specific nested group path
   */
  static getFieldsForNestedGroupPath(fields: any[], groupPath: string): any[] {
    return fields.filter((field) => field.Group && field.Group.trim() === groupPath.trim());
  }

  /**
   * Static method to get child groups for a parent group
   */
  static getChildGroups(fields: any[], parentGroup: string): string[] {
    const childGroups = new Set<string>();
    fields.forEach(field => {
      if (field.Group) {
        const parsed = NestedGroupComponent.parseNestedGroupPath(field.Group);
        if (parsed.isNested && parsed.parent === parentGroup && parsed.child) {
          childGroups.add(parsed.child);
        }
      }
    });
    return Array.from(childGroups);
  }

  /**
   * Static method to check if this is the first field in a nested group
   */
  static isFirstFieldInNestedGroup(field: any, fields: any[]): boolean {
    if (!field.Group) return false;

    const parsed = NestedGroupComponent.parseNestedGroupPath(field.Group);
    if (!parsed.isNested || !parsed.child) return false;

    // Find the first field that belongs to this specific nested group path
    return (
      fields.findIndex((f) => f.Group && f.Group.trim() === field.Group.trim()) ===
      fields.indexOf(field)
    );
  }
}