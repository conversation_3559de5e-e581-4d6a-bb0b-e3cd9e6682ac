/* Success message styling */
.success-message {
  color: #28a745;
  font-size: 14px;
  font-weight: bold;
  padding: 10px;
  border: 1px solid #c3e6cb;
  background-color: #d4edda;
  border-radius: 8px;
}

/* Popup styling */
.popup {
  position: fixed; 
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%; 
  max-width: 400px; 
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5); 
  z-index: 100; 
}

.popup-content {
  text-align: center;
}

.close {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  transition: color 0.3s ease;
}

.close:hover {
  color: #000;
}

.popup-content p {
  margin: 0;
  font-size: 16px;
  color: #333;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
}

/* Responsive popup styling */
@media (max-width: 768px) {
  .popup {
    width: 90%;
    max-width: 350px;
    padding: 16px;
  }
  
  .popup-content p {
    font-size: 14px;
  }
  
  .close {
    font-size: 18px;
    top: 8px;
    right: 12px;
  }
}

@media (max-width: 480px) {
  .popup {
    width: 95%;
    max-width: 300px;
    padding: 12px;
  }
  
  .popup-content p {
    font-size: 13px;
  }
  
  .close {
    font-size: 16px;
    top: 6px;
    right: 10px;
  }
  
  .success-message {
    font-size: 12px;
    padding: 8px;
  }
} 