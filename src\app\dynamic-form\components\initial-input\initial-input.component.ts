import { Component, Input, Output, EventEmitter, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-initial-input',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './initial-input.component.html',
  styleUrls: ['./initial-input.component.scss']
})
export class InitialInputComponent {
  @Input() form!: FormGroup;
  @Input() idField: any = null;
  @Input() isViewMode: boolean = false;
  @Input() showIdDropdown: boolean = false;
  @Input() filteredIdOptions: any[] = [];
  @Input() showValidation: boolean = false;
  @Input() tableName?: string;
  @Input() screenName?: string;
  
  @Output() idInputChange = new EventEmitter<Event>();
  @Output() idInputFocus = new EventEmitter<void>();
  @Output() idInputBlur = new EventEmitter<void>();
  @Output() toggleIdDropdown = new EventEmitter<void>();
  @Output() selectIdOption = new EventEmitter<any>();
  @Output() loadDataAndBuildForm = new EventEmitter<void>();
  @Output() viewData = new EventEmitter<void>();

  getInputClass(): string {
    return this.showValidation ? 'invalid-input' : '';
  }
} 