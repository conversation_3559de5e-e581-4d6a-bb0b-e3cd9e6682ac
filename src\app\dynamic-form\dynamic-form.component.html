<!-- Use success popup component for better modularity -->
<app-success-popup 
  [showSuccessPopup]="showSuccessPopup" 
  [successMessage]="successMessage"
  (closePopup)="closeSuccessPopup()">
</app-success-popup>

@if (!submissionSuccess) {
  @if (showInitialInput) {
    <!-- Use initial input component for better modularity -->
    <app-initial-input
      [form]="form"
      [isViewMode]="isViewMode"
      [showIdDropdown]="showIdDropdown"
      [filteredIdOptions]="filteredIdOptions"
      (idInputChange)="onIdInputChange($event)"
      (idInputFocus)="onIdInputFocus()"
      (idInputBlur)="onIdInputBlur()"
      (toggleIdDropdown)="toggleIdDropdown()"
      (selectIdOption)="selectIdOption($event)"
      (loadDataAndBuildForm)="loadDataAndBuildForm()"
      (viewData)="viewData()">
    </app-initial-input>
  }

  @if (errorMessage) {
    <!-- Use error message component for better modularity -->
    <app-error-message [errorMessage]="errorMessage"></app-error-message>
  }

  @if (!showInitialInput) {
    <div class="form-grid">
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <!-- Use form-actions component for better modularity -->
      <app-form-actions
        [isViewMode]="isViewMode"
        [isRowView]="isRowView"
        [errorMessage]="errorMessage"
        [idValue]="form.get('ID')?.value || ''"
        (toggleViewMode)="toggleViewMode()"
        (onSubmit)="onSubmit()"
        (validateRecord)="validateRecord()"
        (authorizeRecord)="authorizeRecord()"
        (goBack)="goBack()"
        (rejectRecord)="rejectRecord()"
        (deleteRecord)="deleteRecord()">
      </app-form-actions>
      <!-- 🔹 All Fields in Order (Unified Rendering) -->
      <div class="form-grid" [ngClass]="'columns-' + columnCount">
        <div class="form-column" *ngFor="let column of columns">
          <ng-container *ngFor="let field of column">
          <!-- Skip ID field as it's handled separately -->
          @if (field.fieldName?.toUpperCase() !== 'ID') {

            <!-- 🔸 Non-Grouped Fields -->
            @if (!field.Group) {
              <div class="form-field">

                <!-- 🔸 Regular Field -->
                @if (!field.isMulti) {
                  <label [for]="field.fieldName">{{ field.label || field.fieldName }} @if (field.mandatory) {<span>*</span>}
                @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
               </label>

                  <!-- Use field-renderer component for all field types -->
                  <app-field-renderer
                    [field]="field"
                    [form]="form"
                    [isViewMode]="isViewMode">
                  </app-field-renderer>
                }

                <!-- 🔸 Multi-Field (Non-Grouped) -->
                @if (field.isMulti) {
                  <app-multi-field
                    [field]="field"
                    [form]="form"
                    [isViewMode]="isViewMode"
                    (multiFieldAdded)="onMultiFieldAdded($event)"
                    (multiFieldRemoved)="onMultiFieldRemoved($event)">
                  </app-multi-field>
                }

              </div>
            }

            <!-- 🔸 Grouped Fields -->
            @if (field.Group && isFirstFieldInParentGroup(field)) {
              <app-group-field
                [field]="field"
                [form]="form"
                [isViewMode]="isViewMode"
                [isRowView]="isRowView"
                [fields]="fields"
                (groupAdded)="onGroupAdded($event)"
                (groupRemoved)="onGroupRemoved($event)"
                (groupCloned)="onGroupCloned($event)">
              </app-group-field>
            }

          }
          </ng-container>
        </div>
      </div>
    </form>
    </div>
  }
} @else {
  <div class="success-message">Record submitted successfully!</div>
}
