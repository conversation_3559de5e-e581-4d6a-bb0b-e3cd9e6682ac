<!-- Use success popup component for better modularity -->
<app-success-popup 
  [showSuccessPopup]="showSuccessPopup" 
  [successMessage]="successMessage"
  (closePopup)="closeSuccessPopup()">
</app-success-popup>

@if (!submissionSuccess) {
  @if (showInitialInput) {
    <!-- Use initial input component for better modularity -->
    <app-initial-input
      [form]="form"
      [isViewMode]="isViewMode"
      [showIdDropdown]="showIdDropdown"
      [filteredIdOptions]="filteredIdOptions"
      (idInputChange)="onIdInputChange($event)"
      (idInputFocus)="onIdInputFocus()"
      (idInputBlur)="onIdInputBlur()"
      (toggleIdDropdown)="toggleIdDropdown()"
      (selectIdOption)="selectIdOption($event)"
      (loadDataAndBuildForm)="loadDataAndBuildForm()"
      (viewData)="viewData()">
    </app-initial-input>
  }

  @if (errorMessage) {
    <!-- Use error message component for better modularity -->
    <app-error-message [errorMessage]="errorMessage"></app-error-message>
  }

  @if (!showInitialInput) {
    <div class="form-grid">
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div class="horizontal-container">
      <div class="form-field">
        <!-- <label>ID</label> -->
        <p>{{ form.get('ID')?.value }}</p>
      </div>

      <div class="button-group">
          <!-- View toggle button -->
          <button mat-raised-button color="primary" type="button" (click)="toggleViewMode()" matTooltip="Toggle View"
                  class="form-action-button toggle-view-button">
            <mat-icon>{{ isRowView ? 'view_list' : 'view_module' }}</mat-icon>
            {{ isRowView ? 'Nested View' : 'Row View' }}
          </button>

          <!-- submit button -->
          @if (!isViewMode) {
            <button mat-raised-button color="primary" type="submit" [disabled]="isViewMode" matTooltip="Submit"
                    class="form-action-button submit-button">
              <mat-icon>send</mat-icon>
            </button>
          }

          <!-- Validate button -->
          @if (!isViewMode) {
            <button mat-raised-button color="accent" type="button" (click)="validateRecord()" matTooltip="Validate"
                    class="form-action-button validate-button">
              <mat-icon>check_circle</mat-icon>
            </button>
          }

          <!-- Authorize button -->
          @if (!isViewMode) {
            <button mat-raised-button color="accent" type="button" (click)="authorizeRecord()" matTooltip="Authorize"
                    class="form-action-button authorize-button">
              <mat-icon>verified</mat-icon>
            </button>
          }

          <!-- back button -->
          <button mat-raised-button color="primary" type="button" (click)="goBack()" matTooltip="Back"
                  class="form-action-button back-button">
            <mat-icon>arrow_back</mat-icon>
          </button>

          <!-- Reject button -->
          @if (!isViewMode) {
            <button mat-raised-button color="warn" type="button" matTooltip="Reject"
                    class="form-action-button reject-button">
              <mat-icon>cancel</mat-icon>
            </button>
          }

          <!-- Delete button -->
          @if (!isViewMode) {
            <button mat-raised-button color="warn" type="button" matTooltip="Delete"
                    class="form-action-button delete-button">
              <mat-icon>delete</mat-icon>
            </button>
          }
        @if (errorMessage) {
          <div class="error-message">{{ errorMessage }}</div>
        }
      </div>


    </div>
      <!-- 🔹 All Fields in Order (Unified Rendering) -->
      <div class="form-grid" [ngClass]="'columns-' + columnCount">
        <div class="form-column" *ngFor="let column of columns">
          <ng-container *ngFor="let field of column">
          <!-- Skip ID field as it's handled separately -->
          @if (field.fieldName?.toUpperCase() !== 'ID') {

            <!-- 🔸 Non-Grouped Fields -->
            @if (!field.Group) {
              <div class="form-field">

                <!-- 🔸 Regular Field -->
                @if (!field.isMulti) {
                  <label [for]="field.fieldName">{{ field.label || field.fieldName }} @if (field.mandatory) {<span>*</span>}
                @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
               </label>

                  @if (field.foreginKey) {
                    <!-- Check if this is a type field (fieldType) -->
                    @if (field.foreginKey === 'fieldType') {
                      <div class="dropdown-input-container">
                        <input [formControlName]="field.fieldName" [id]="field.fieldName" type="text" 
                               class="form-input dropdown-input" 
                               (input)="onTypeInputChange($event, field.fieldName)" 
                               (focus)="onTypeInputFocus(field.fieldName)" 
                               (blur)="onTypeInputBlur(field.fieldName)"
                               [disabled]="isViewMode || field.noInput"
                               [placeholder]="'Search ' + field.label?.trim() || field.fieldName" />
                        
                        <!-- Arrow button to toggle dropdown -->
                        <button type="button" class="dropdown-arrow-btn" 
                                (click)="toggleTypeDropdown(field.fieldName)" 
                                [disabled]="isViewMode || field.noInput"
                                matTooltip="Show type suggestions">
                          <mat-icon>{{ showTypeDropdown[field.fieldName] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                        </button>
                        
                        <!-- Dropdown list for filtered results -->
                        @if (showTypeDropdown[field.fieldName]) {
                          <div class="dropdown-list">
                            @if (filteredTypeOptions[field.fieldName] && filteredTypeOptions[field.fieldName].length > 0) {
                              @for (option of filteredTypeOptions[field.fieldName]; track option.ROW_ID) {
                                <div class="dropdown-item" (click)="selectTypeOption(option, field.fieldName)">
                                  {{ option.ROW_ID }}
                                </div>
                              }
                            } @else {
                              <div class="dropdown-empty">
                                No types found
                              </div>
                            }
                          </div>
                        }
                      </div>
                    }
                    <!-- Check if this is a foreign key field (formDefinition) -->
                    @else if (field.foreginKey === 'formDefinition') {
                      <div class="dropdown-input-container">
                        <input [formControlName]="field.fieldName" [id]="field.fieldName" type="text" 
                               class="form-input dropdown-input" 
                               (input)="onForeignKeyInputChange($event, field.fieldName)" 
                               (focus)="onForeignKeyInputFocus(field.fieldName)" 
                               (blur)="onForeignKeyInputBlur(field.fieldName)"
                               [disabled]="isViewMode || field.noInput"
                              [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
                        
                              
                        <!-- Arrow button to toggle dropdown -->
                        <button type="button" class="dropdown-arrow-btn" 
                                (click)="toggleForeignKeyDropdown(field.fieldName)" 
                                [disabled]="isViewMode || field.noInput"
                                matTooltip="Show foreign key suggestions">
                          <mat-icon>{{ showForeignKeyDropdown[field.fieldName] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                        </button>
                        
                        <!-- Dropdown list for filtered results -->
                        @if (showForeignKeyDropdown[field.fieldName]) {
                          <div class="dropdown-list">
                            @if (filteredForeignKeyOptions[field.fieldName] && filteredForeignKeyOptions[field.fieldName].length > 0) {
                              @for (option of filteredForeignKeyOptions[field.fieldName]; track option.ROW_ID) {
                                <div class="dropdown-item" (click)="selectForeignKeyOption(option, field.fieldName)">
                                  {{ option.ROW_ID }}
                                </div>
                              }
                            } @else {
                              <div class="dropdown-empty">
                                No foreign keys found
                              </div>
                            }
                          </div>
                        }
                      </div>
                    }
                    <!-- Default dropdown for other foreign keys -->
                    @else {
                      <div class="dropdown-input-container">
                        <input [formControlName]="field.fieldName" [id]="field.fieldName" type="text" 
                               class="form-input dropdown-input" 
                               (input)="onRegularInputChange($event, field.fieldName)" 
                               (focus)="onRegularInputFocus(field.fieldName)" 
                               (blur)="onRegularInputBlur(field.fieldName)"
                               [disabled]="isViewMode || field.noInput"
                               [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
                        
                        <!-- Arrow button to toggle dropdown -->
                        <button type="button" class="dropdown-arrow-btn" 
                                (click)="toggleRegularDropdown(field.fieldName)" 
                                [disabled]="isViewMode || field.noInput"
                                matTooltip="Show options">
                          <mat-icon>{{ showRegularDropdown[field.fieldName] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                        </button>
                        
                        <!-- Dropdown list for filtered results -->
                        @if (showRegularDropdown[field.fieldName]) {
                          <div class="dropdown-list">
                            @if (filteredRegularOptions[field.fieldName] && filteredRegularOptions[field.fieldName].length > 0) {
                              @for (option of filteredRegularOptions[field.fieldName]; track option.ROW_ID) {
                                <div class="dropdown-item" (click)="selectRegularOption(option, field.fieldName)">
                                  @for (key of getKeys(option); track key) {
                                    {{ option[key] }}&nbsp;
                                  }
                                </div>
                              }
                            } @else {
                              <div class="dropdown-empty">
                                No options found
                              </div>
                            }
                          </div>
                        }
                      </div>
                    }
                  } @else {
                    <!-- Regular input fields for non-foreign key fields -->
                    @if (field.type === 'boolean') {
                      <input [formControlName]="field.fieldName" [id]="field.fieldName"
                        type="checkbox" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
                    }
                    @if (field.type === 'string') {
                      <input [formControlName]="field.fieldName" [id]="field.fieldName"
                        type="text" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" [placeholder]="(field.label?.trim() || field.fieldName)" />
                    }
                    @if (field.type === 'int') {
                      <input [formControlName]="field.fieldName" [id]="field.fieldName"
                        type="number" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
                    }
                    @if (field.type === 'date') {
                      <input [formControlName]="field.fieldName" [id]="field.fieldName"
                        type="date" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
                    }
                    @if (field.type === 'double') {
                      <input [formControlName]="field.fieldName" [id]="field.fieldName"
                        type="number" step="00.50" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
                    }
                  }
                }

                <!-- 🔸 Multi-Field (Non-Grouped) -->
                @if (field.isMulti) {
                  <div [formArrayName]="field.fieldName">
                    @for (control of getMultiArray(field.fieldName).controls; track control; let j = $index) {
                      <div [formGroupName]="j" class="form-field is-multi">
                        <label>{{ field.fieldName }} ({{ j + 1 }})
                          @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                        </label>

                        <div class="multi-input-container">
                          <div class="multi-input">
                            @if (field.foreginKey) {
                              <!-- Check if this is a type field (fieldType) -->
                              @if (field.foreginKey === 'fieldType') {
                                <div class="dropdown-input-container">
                                  <input [formControlName]="field.fieldName" type="text"
                                         class="form-input dropdown-input"
                                         (input)="onTypeInputChange($event, field.fieldName + '_' + j)"
                                         (focus)="onTypeInputFocus(field.fieldName + '_' + j)"
                                         (blur)="onTypeInputBlur(field.fieldName + '_' + j)"
                                         [disabled]="field.noInput"
                                         [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />

                                  <button type="button" class="dropdown-arrow-btn"
                                          (click)="toggleTypeDropdown(field.fieldName + '_' + j)"
                                          [disabled]="field.noInput"
                                          matTooltip="Show type suggestions">
                                    <mat-icon>{{ showTypeDropdown[field.fieldName + '_' + j] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                  </button>

                                  @if (showTypeDropdown[field.fieldName + '_' + j]) {
                                    <div class="dropdown-list">
                                      @if (filteredTypeOptions[field.fieldName + '_' + j] && filteredTypeOptions[field.fieldName + '_' + j].length > 0) {
                                        @for (option of filteredTypeOptions[field.fieldName + '_' + j]; track option.ROW_ID) {
                                          <div class="dropdown-item" (click)="selectTypeOption(option, field.fieldName + '_' + j)">
                                            {{ option.ROW_ID }}
                                          </div>
                                        }
                                      } @else {
                                        <div class="dropdown-empty">
                                          No types found
                                        </div>
                                      }
                                    </div>
                                  }
                                </div>
                              }
                              <!-- Check if this is a foreign key field (formDefinition) -->
                              @else if (field.foreginKey === 'formDefinition') {
                                <div class="dropdown-input-container">
                                  <input [formControlName]="field.fieldName" type="text"
                                         class="form-input dropdown-input"
                                         (input)="onForeignKeyInputChange($event, field.fieldName + '_' + j)"
                                         (focus)="onForeignKeyInputFocus(field.fieldName + '_' + j)"
                                         (blur)="onForeignKeyInputBlur(field.fieldName + '_' + j)"
                                         [disabled]="field.noInput"
                                         [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />

                                  <button type="button" class="dropdown-arrow-btn"
                                          (click)="toggleForeignKeyDropdown(field.fieldName + '_' + j)"
                                          [disabled]="field.noInput"
                                          matTooltip="Show foreign key suggestions">
                                    <mat-icon>{{ showForeignKeyDropdown[field.fieldName + '_' + j] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                  </button>

                                  @if (showForeignKeyDropdown[field.fieldName + '_' + j]) {
                                    <div class="dropdown-list">
                                      @if (filteredForeignKeyOptions[field.fieldName + '_' + j] && filteredForeignKeyOptions[field.fieldName + '_' + j].length > 0) {
                                        @for (option of filteredForeignKeyOptions[field.fieldName + '_' + j]; track option.ROW_ID) {
                                          <div class="dropdown-item" (click)="selectForeignKeyOption(option, field.fieldName + '_' + j)">
                                            {{ option.ROW_ID }}
                                          </div>
                                        }
                                      } @else {
                                        <div class="dropdown-empty">
                                          No foreign keys found
                                        </div>
                                      }
                                    </div>
                                  }
                                </div>
                              }
                              <!-- Default dropdown for other foreign keys -->
                              @else {
                                <div class="dropdown-input-container">
                                  <input [formControlName]="field.fieldName" type="text"
                                         class="form-input dropdown-input"
                                         (input)="onRegularInputChange($event, field.fieldName + '_' + j)"
                                         (focus)="onRegularInputFocus(field.fieldName + '_' + j)"
                                         (blur)="onRegularInputBlur(field.fieldName + '_' + j)"
                                         [disabled]="field.noInput"
                                         [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />

                                  <button type="button" class="dropdown-arrow-btn"
                                          (click)="toggleRegularDropdown(field.fieldName + '_' + j)"
                                          [disabled]="field.noInput"
                                          matTooltip="Show options">
                                    <mat-icon>{{ showRegularDropdown[field.fieldName + '_' + j] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                  </button>

                                  @if (showRegularDropdown[field.fieldName + '_' + j]) {
                                    <div class="dropdown-list">
                                      @if (filteredRegularOptions[field.fieldName + '_' + j] && filteredRegularOptions[field.fieldName + '_' + j].length > 0) {
                                        @for (option of filteredRegularOptions[field.fieldName + '_' + j]; track option.ROW_ID) {
                                          <div class="dropdown-item" (click)="selectRegularOption(option, field.fieldName + '_' + j)">
                                            @for (key of getKeys(option); track key) {
                                              {{ option[key] }}&nbsp;
                                            }
                                          </div>
                                        }
                                      } @else {
                                        <div class="dropdown-empty">
                                          No options found
                                        </div>
                                      }
                                    </div>
                                  }
                                </div>
                              }
                            } @else {
                              <!-- Regular input fields for non-foreign key multi-fields -->
                              @if (field.type === 'string') {
                                <input [formControlName]="field.fieldName" type="text"
                                  [placeholder]="(field.label?.trim() || field.fieldName) + '-' + (j+1)" [disabled]="field.noInput" />
                              }
                              @if (field.type === 'int') {
                                <input [formControlName]="field.fieldName" type="number" [disabled]="field.noInput" />
                              }
                              @if (field.type === 'boolean') {
                                <input [formControlName]="field.fieldName" type="checkbox" [disabled]="field.noInput" />
                              }
                              @if (field.type === 'date') {
                                <input [formControlName]="field.fieldName" type="date" [disabled]="field.noInput" />
                              }
                              @if (field.type === 'double') {
                                <input [formControlName]="field.fieldName" type="number"
                                  step="00.50" [disabled]="field.noInput" />
                              }
                            }
                          </div>

                          <div class="multi-buttons">
                            @if (getMultiArray(field.fieldName).length > 1 && !isViewMode && !field.noInput) {
                              <button mat-icon-button color="warn" type="button" (click)="removeMultiField(field.fieldName, j)" matTooltip="Delete">
                                <mat-icon>delete</mat-icon>
                              </button>
                            }

                            @if (!isViewMode && !field.noInput) {
                              <button mat-icon-button color="primary" type="button" (click)="addMultiField(field, undefined, j)" matTooltip="Add">
                                <mat-icon>add</mat-icon>
                              </button>
                            }
                          </div>
                        </div>
                      </div>
                    }
                  </div>
                }

              </div>
            }

            <!-- 🔸 Grouped Fields -->
            @if (field.Group && isFirstFieldInParentGroup(field)) {
              @let parsed = parseGroupPath(field.Group);
              @if (parsed.parent) {
                <div [formArrayName]="parsed.parent" class="grouped-field-section">
                  <h3>{{ parsed.parent }}</h3>
                  @for (group of getGroupArray(parsed.parent).controls; track group; let k = $index) {
                    <div [formGroupName]="k" [class]="isRowView ? 'form-grid row-view-table' : 'form-grid multi-field'">

                      @if (isRowView) {
                        <!-- Row View: All fields in a single table-like row -->
                        <div class="row-view-table-container">
                          <!-- Parent group fields -->
                          @for (groupField of getFieldsForGroup(parsed.parent); track groupField.fieldName) {
                            @if (!groupField.isMulti) {
                              <div class="row-view-table-cell">
                                <label>{{ groupField.fieldName }} @if (groupField.mandatory) {<span>*</span>}
                                  @if (groupField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                                </label>
                                @if (groupField.foreginKey) {
                                  <!-- Check if this is a type field (fieldType) -->
                                  @if (groupField.foreginKey === 'fieldType') {
                                    <div class="dropdown-input-container">
                                      <input [formControlName]="groupField.fieldName" type="text"
                                             class="form-input dropdown-input"
                                             (input)="onTypeInputChange($event, groupField.fieldName + '_group_' + k)"
                                             (focus)="onTypeInputFocus(groupField.fieldName + '_group_' + k)"
                                             (blur)="onTypeInputBlur(groupField.fieldName + '_group_' + k)"
                                             [disabled]="groupField.noInput"
                                             [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />

                                      <button type="button" class="dropdown-arrow-btn"
                                              (click)="toggleTypeDropdown(groupField.fieldName + '_group_' + k)"
                                              [disabled]="groupField.noInput"
                                              matTooltip="Show type suggestions">
                                        <mat-icon>{{ showTypeDropdown[groupField.fieldName + '_group_' + k] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                      </button>

                                      @if (showTypeDropdown[groupField.fieldName + '_group_' + k]) {
                                        <div class="dropdown-list">
                                          @if (filteredTypeOptions[groupField.fieldName + '_group_' + k] && filteredTypeOptions[groupField.fieldName + '_group_' + k].length > 0) {
                                            @for (option of filteredTypeOptions[groupField.fieldName + '_group_' + k]; track option.ROW_ID) {
                                              <div class="dropdown-item" (click)="selectTypeOption(option, groupField.fieldName + '_group_' + k)">
                                                {{ option.ROW_ID }}
                                              </div>
                                            }
                                          } @else {
                                            <div class="dropdown-empty">
                                              No types found
                                            </div>
                                          }
                                        </div>
                                      }
                                    </div>
                                  }
                                  <!-- Check if this is a foreign key field (formDefinition) -->
                                  @else if (groupField.foreginKey === 'formDefinition') {
                                    <div class="dropdown-input-container">
                                      <input [formControlName]="groupField.fieldName" type="text"
                                             class="form-input dropdown-input"
                                             (input)="onForeignKeyInputChange($event, groupField.fieldName + '_group_' + k)"
                                             (focus)="onForeignKeyInputFocus(groupField.fieldName + '_group_' + k)"
                                             (blur)="onForeignKeyInputBlur(groupField.fieldName + '_group_' + k)"
                                             [disabled]="groupField.noInput"
                                             [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />

                                      <button type="button" class="dropdown-arrow-btn"
                                              (click)="toggleForeignKeyDropdown(groupField.fieldName + '_group_' + k)"
                                              [disabled]="groupField.noInput"
                                              matTooltip="Show foreign key suggestions">
                                        <mat-icon>{{ showForeignKeyDropdown[groupField.fieldName + '_group_' + k] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                      </button>

                                      @if (showForeignKeyDropdown[groupField.fieldName + '_group_' + k]) {
                                        <div class="dropdown-list">
                                          @if (filteredForeignKeyOptions[groupField.fieldName + '_group_' + k] && filteredForeignKeyOptions[groupField.fieldName + '_group_' + k].length > 0) {
                                            @for (option of filteredForeignKeyOptions[groupField.fieldName + '_group_' + k]; track option.ROW_ID) {
                                              <div class="dropdown-item" (click)="selectForeignKeyOption(option, groupField.fieldName + '_group_' + k)">
                                                {{ option.ROW_ID }}
                                              </div>
                                            }
                                          } @else {
                                            <div class="dropdown-empty">
                                              No foreign keys found
                                            </div>
                                          }
                                        </div>
                                      }
                                    </div>
                                  }
                                  <!-- Default dropdown for other foreign keys -->
                                  @else {
                                    <div class="dropdown-input-container">
                                      <input [formControlName]="groupField.fieldName" type="text"
                                             class="form-input dropdown-input"
                                             (input)="onRegularInputChange($event, groupField.fieldName + '_group_' + k)"
                                             (focus)="onRegularInputFocus(groupField.fieldName + '_group_' + k)"
                                             (blur)="onRegularInputBlur(groupField.fieldName + '_group_' + k)"
                                             [disabled]="groupField.noInput"
                                             [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />

                                      <button type="button" class="dropdown-arrow-btn"
                                              (click)="toggleRegularDropdown(groupField.fieldName + '_group_' + k)"
                                              [disabled]="groupField.noInput"
                                              matTooltip="Show options">
                                        <mat-icon>{{ showRegularDropdown[groupField.fieldName + '_group_' + k] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                      </button>

                                      @if (showRegularDropdown[groupField.fieldName + '_group_' + k]) {
                                        <div class="dropdown-list">
                                          @if (filteredRegularOptions[groupField.fieldName + '_group_' + k] && filteredRegularOptions[groupField.fieldName + '_group_' + k].length > 0) {
                                            @for (option of filteredRegularOptions[groupField.fieldName + '_group_' + k]; track option.ROW_ID) {
                                              <div class="dropdown-item" (click)="selectRegularOption(option, groupField.fieldName + '_group_' + k)">
                                                @for (key of getKeys(option); track key) {
                                                  {{ option[key] }}&nbsp;
                                                }
                                              </div>
                                            }
                                          } @else {
                                            <div class="dropdown-empty">
                                              No options found
                                            </div>
                                          }
                                        </div>
                                      }
                                    </div>
                                  }
                                } @else {
                                  <!-- Regular input fields for non-foreign key fields -->
                                  @if (groupField.type === 'boolean') {
                                    <input [formControlName]="groupField.fieldName" type="checkbox" [disabled]="groupField.noInput" />
                                  }
                                  @if (groupField.type === 'string') {
                                    <input [formControlName]="groupField.fieldName" type="text" [disabled]="groupField.noInput" [placeholder]="(groupField.label?.trim() || groupField.fieldName)" />
                                  }
                                  @if (groupField.type === 'int') {
                                    <input [formControlName]="groupField.fieldName" type="number" [disabled]="groupField.noInput" />
                                  }
                                  @if (groupField.type === 'date') {
                                    <input [formControlName]="groupField.fieldName" type="date" [disabled]="groupField.noInput" />
                                  }
                                  @if (groupField.type === 'double') {
                                    <input [formControlName]="groupField.fieldName" type="number" step="00.50" [disabled]="groupField.noInput" />
                                  }
                                }
                              </div>
                            }
                          }
                        </div>
                      } @else {
                        <!-- Nested View: Fields in their natural groups -->
                        @for (groupField of getFieldsForGroup(parsed.parent); track groupField.fieldName) {
                          @if (!groupField.isMulti) {
                            <div class="form-field">
                              <label>{{ groupField.fieldName }} @if (groupField.mandatory) {<span>*</span>}
                                @if (groupField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                              </label>
                              @if (groupField.foreginKey) {
                                <!-- Similar dropdown structure as above for nested view -->
                                <div class="dropdown-input-container">
                                  <input [formControlName]="groupField.fieldName" type="text"
                                         class="form-input dropdown-input"
                                         [disabled]="groupField.noInput"
                                         [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />
                                </div>
                              } @else {
                                <!-- Regular input fields -->
                                @if (groupField.type === 'string') {
                                  <input [formControlName]="groupField.fieldName" type="text" [disabled]="groupField.noInput" />
                                }
                                @if (groupField.type === 'int') {
                                  <input [formControlName]="groupField.fieldName" type="number" [disabled]="groupField.noInput" />
                                }
                                @if (groupField.type === 'boolean') {
                                  <input [formControlName]="groupField.fieldName" type="checkbox" [disabled]="groupField.noInput" />
                                }
                                @if (groupField.type === 'date') {
                                  <input [formControlName]="groupField.fieldName" type="date" [disabled]="groupField.noInput" />
                                }
                                @if (groupField.type === 'double') {
                                  <input [formControlName]="groupField.fieldName" type="number" step="00.50" [disabled]="groupField.noInput" />
                                }
                              }
                            </div>
                          }
                        }
                      }

                      <!-- Group action buttons -->
                      <div class="button-group">
                        @if (getGroupArray(parsed.parent).length > 1 && !isViewMode) {
                          <button mat-icon-button color="warn" type="button" (click)="removeGroup(parsed.parent, k)" matTooltip="Delete Group">
                            <mat-icon>delete</mat-icon>
                          </button>
                        }

                        @if (!isViewMode) {
                          <button mat-icon-button color="primary" type="button" (click)="addGroup(parsed.parent, k)" matTooltip="Add Group">
                            <mat-icon>add</mat-icon>
                          </button>
                        }

                        @if (!isViewMode) {
                          <button mat-icon-button color="accent" type="button" (click)="cloneGroup(parsed.parent, k)" matTooltip="Clone Group">
                            <mat-icon>content_copy</mat-icon>
                          </button>
                        }
                      </div>
                    </div>
                  }
                </div>
              }
            }

          }
          </ng-container>
        </div>
      </div>
    </form>
    </div>
  }
} @else {
  <div class="success-message">Record submitted successfully!</div>
}
