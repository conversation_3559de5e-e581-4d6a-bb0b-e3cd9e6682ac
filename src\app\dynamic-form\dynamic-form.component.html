<!-- Use success popup component for better modularity -->
<app-success-popup 
  [showSuccessPopup]="showSuccessPopup" 
  [successMessage]="successMessage"
  (closePopup)="closeSuccessPopup()">
</app-success-popup>

@if (!submissionSuccess) {
  @if (showInitialInput) {
    <!-- Use initial input component for better modularity -->
    <app-initial-input
      [form]="form"
      [isViewMode]="isViewMode"
      [showIdDropdown]="showIdDropdown"
      [filteredIdOptions]="filteredIdOptions"
      (idInputChange)="onIdInputChange($event)"
      (idInputFocus)="onIdInputFocus()"
      (idInputBlur)="onIdInputBlur()"
      (toggleIdDropdown)="toggleIdDropdown()"
      (selectIdOption)="selectIdOption($event)"
      (loadDataAndBuildForm)="loadDataAndBuildForm()"
      (viewData)="viewData()">
    </app-initial-input>
  }

  @if (errorMessage) {
    <!-- Use error message component for better modularity -->
    <app-error-message [errorMessage]="errorMessage"></app-error-message>
  }

  @if (!showInitialInput) {
    <div class="form-grid">
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <!-- Use form-actions component for better modularity -->
      <app-form-actions
        [isViewMode]="isViewMode"
        [isRowView]="isRowView"
        [errorMessage]="errorMessage"
        [idValue]="form.get('ID')?.value || ''"
        (toggleViewMode)="toggleViewMode()"
        (onSubmit)="onSubmit()"
        (validateRecord)="validateRecord()"
        (authorizeRecord)="authorizeRecord()"
        (goBack)="goBack()"
        (rejectRecord)="rejectRecord()"
        (deleteRecord)="deleteRecord()">
      </app-form-actions>
      <!-- Use form-container component for better modularity -->
      <app-form-container
        [form]="form"
        [columns]="columns"
        [columnCount]="columnCount"
        [isViewMode]="isViewMode"
        [isRowView]="isRowView"
        [fields]="fields"
        [idField]="idField"
        [isIdFieldVisible]="false"
        (multiFieldAdded)="onMultiFieldAdded($event)"
        (multiFieldRemoved)="onMultiFieldRemoved($event)"
        (groupAdded)="onGroupAdded($event)"
        (groupRemoved)="onGroupRemoved($event)"
        (groupCloned)="onGroupCloned($event)">
      </app-form-container>
    </form>
    </div>
  }
} @else {
  <div class="success-message">Record submitted successfully!</div>
}
