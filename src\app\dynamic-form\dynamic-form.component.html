<app-success-popup 
  [showSuccessPopup]="showSuccessPopup" 
  [successMessage]="successMessage"
  (closePopup)="closeSuccessPopup()">
</app-success-popup>

@if (!submissionSuccess) {
  @if (showInitialInput) {
    <app-initial-input
      [form]="form"
      [isViewMode]="isViewMode"
      [showIdDropdown]="showIdDropdown"
      [filteredIdOptions]="filteredIdOptions"
      (idInputChange)="onIdInputChange($event)"
      (idInputFocus)="onIdInputFocus()"
      (idInputBlur)="onIdInputBlur()"
      (toggleIdDropdown)="toggleIdDropdown()"
      (selectIdOption)="selectIdOption($event)"
      (loadDataAndBuildForm)="loadDataAndBuildForm()"
      (viewData)="viewData()">
    </app-initial-input>
  }

  @if (errorMessage) {
    <app-error-message [errorMessage]="errorMessage"></app-error-message>
  }

  @if (!showInitialInput) {
    <app-form-container
      [form]="form"
      [columns]="columns"
      [columnCount]="columnCount"
      [isViewMode]="isViewMode"
      [isRowView]="isRowView"
      [showTypeDropdown]="showTypeDropdown"
      [showForeignKeyDropdown]="showForeignKeyDropdown"
      [showRegularDropdown]="showRegularDropdown"
      [filteredTypeOptions]="filteredTypeOptions"
      [filteredForeignKeyOptions]="filteredForeignKeyOptions"
      [filteredRegularOptions]="filteredRegularOptions"
      [getMultiArray]="getMultiArray"
      [getFieldsForGroup]="getFieldsForGroup"
      [getGroupArray]="getGroupArray"
      [parseGroupPath]="parseGroupPath"
      [isFirstFieldInParentGroup]="isFirstFieldInParentGroup"
      [idField]="idField"
      [isIdFieldVisible]="false"
      (addMultiField)="addMultiField($event.field, undefined, $event.index)"
      (removeMultiField)="handleRemoveMultiField($event)"
      (addGroup)="addGroup($event.groupName, $event.index)"
      (removeGroup)="removeGroup($event.groupName, $event.index)"
      (cloneGroup)="cloneGroup($event.groupName, $event.index)"
      (typeInputChange)="onTypeInputChange($event.event, $event.fieldName)"
      (typeInputFocus)="onTypeInputFocus($event)"
      (typeInputBlur)="onTypeInputBlur($event)"
      (toggleTypeDropdown)="toggleTypeDropdown($event)"
      (selectTypeOption)="selectTypeOption($event.option, $event.fieldName)"
      (foreignKeyInputChange)="onForeignKeyInputChange($event.event, $event.fieldName)"
      (foreignKeyInputFocus)="onForeignKeyInputFocus($event)"
      (foreignKeyInputBlur)="onForeignKeyInputBlur($event)"
      (toggleForeignKeyDropdown)="toggleForeignKeyDropdown($event)"
      (selectForeignKeyOption)="selectForeignKeyOption($event.option, $event.fieldName)"
      (regularInputChange)="onRegularInputChange($event.event, $event.fieldName)"
      (regularInputFocus)="onRegularInputFocus($event)"
      (regularInputBlur)="onRegularInputBlur($event)"
      (toggleRegularDropdown)="toggleRegularDropdown($event)"
      (selectRegularOption)="selectRegularOption($event.option, $event.fieldName)">
    </app-form-container>

    <app-form-actions
      [isViewMode]="isViewMode"
      [isRowView]="isRowView"
      [errorMessage]="errorMessage"
      (toggleViewMode)="toggleViewMode()"
      (onSubmit)="onSubmit()"
      (validateRecord)="validateRecord()"
      (authorizeRecord)="authorizeRecord()"
      (goBack)="goBack()"
      (rejectRecord)="rejectRecord()"
      (deleteRecord)="deleteRecord()">
    </app-form-actions>
  }
} @else {
  <div class="success-message">Record submitted successfully!</div>
}
