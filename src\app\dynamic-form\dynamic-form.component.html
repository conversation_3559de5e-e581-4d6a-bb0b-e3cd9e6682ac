<!-- Use success popup component for better modularity -->
<app-success-popup 
  [showSuccessPopup]="showSuccessPopup" 
  [successMessage]="successMessage"
  (closePopup)="closeSuccessPopup()">
</app-success-popup>

@if (!submissionSuccess) {
  @if (showInitialInput) {
    <!-- Use initial input component for better modularity -->
    <app-initial-input
      [form]="form"
      [isViewMode]="isViewMode"
      [showIdDropdown]="showIdDropdown"
      [filteredIdOptions]="filteredIdOptions"
      (idInputChange)="onIdInputChange($event)"
      (idInputFocus)="onIdInputFocus()"
      (idInputBlur)="onIdInputBlur()"
      (toggleIdDropdown)="toggleIdDropdown()"
      (selectIdOption)="selectIdOption($event)"
      (loadDataAndBuildForm)="loadDataAndBuildForm()"
      (viewData)="viewData()">
    </app-initial-input>
  }

  @if (errorMessage) {
    <!-- Use error message component for better modularity -->
    <app-error-message [errorMessage]="errorMessage"></app-error-message>
  }

  @if (!showInitialInput) {
    <div class="form-grid">
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <!-- Use form-actions component for better modularity -->
      <app-form-actions
        [isViewMode]="isViewMode"
        [isRowView]="isRowView"
        [errorMessage]="errorMessage"
        [idValue]="form.get('ID')?.value || ''"
        (toggleViewMode)="toggleViewMode()"
        (onSubmit)="onSubmit()"
        (validateRecord)="validateRecord()"
        (authorizeRecord)="authorizeRecord()"
        (goBack)="goBack()"
        (rejectRecord)="rejectRecord()"
        (deleteRecord)="deleteRecord()">
      </app-form-actions>
      <!-- 🔹 All Fields in Order (Unified Rendering) -->
      <div class="form-grid" [ngClass]="'columns-' + columnCount">
        <div class="form-column" *ngFor="let column of columns">
          <ng-container *ngFor="let field of column">
          <!-- Skip ID field as it's handled separately -->
          @if (field.fieldName?.toUpperCase() !== 'ID') {

            <!-- 🔸 Non-Grouped Fields -->
            @if (!field.Group) {
              <div class="form-field">

                <!-- 🔸 Regular Field -->
                @if (!field.isMulti) {
                  <label [for]="field.fieldName">{{ field.label || field.fieldName }} @if (field.mandatory) {<span>*</span>}
                @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
               </label>

                  @if (field.foreginKey) {
                    <!-- Use dropdown-field component for all dropdown types -->
                    @if (field.foreginKey === 'fieldType') {
                      <app-dropdown-field
                        [field]="field"
                        [form]="form"
                        [isViewMode]="isViewMode"
                        [dropdownType]="'type'"
                        (optionSelected)="onDropdownOptionSelected($event)">
                      </app-dropdown-field>
                    }
                    @else if (field.foreginKey === 'formDefinition') {
                      <app-dropdown-field
                        [field]="field"
                        [form]="form"
                        [isViewMode]="isViewMode"
                        [dropdownType]="'foreignKey'"
                        (optionSelected)="onDropdownOptionSelected($event)">
                      </app-dropdown-field>
                    }
                    @else {
                      <app-dropdown-field
                        [field]="field"
                        [form]="form"
                        [isViewMode]="isViewMode"
                        [dropdownType]="'regular'"
                        (optionSelected)="onDropdownOptionSelected($event)">
                      </app-dropdown-field>
                    }
                  } @else {
                    <!-- Regular input fields for non-foreign key fields -->
                    @if (field.type === 'boolean') {
                      <input [formControlName]="field.fieldName" [id]="field.fieldName"
                        type="checkbox" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
                    }
                    @if (field.type === 'string') {
                      <input [formControlName]="field.fieldName" [id]="field.fieldName"
                        type="text" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" [placeholder]="(field.label?.trim() || field.fieldName)" />
                    }
                    @if (field.type === 'int') {
                      <input [formControlName]="field.fieldName" [id]="field.fieldName"
                        type="number" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
                    }
                    @if (field.type === 'date') {
                      <input [formControlName]="field.fieldName" [id]="field.fieldName"
                        type="date" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
                    }
                    @if (field.type === 'double') {
                      <input [formControlName]="field.fieldName" [id]="field.fieldName"
                        type="number" step="00.50" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
                    }
                  }
                }

                <!-- 🔸 Multi-Field (Non-Grouped) -->
                @if (field.isMulti) {
                  <div [formArrayName]="field.fieldName">
                    @for (control of getMultiArray(field.fieldName).controls; track control; let j = $index) {
                      <div [formGroupName]="j" class="form-field is-multi">
                        <label>{{ field.fieldName }} ({{ j + 1 }})
                          @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                        </label>

                        <div class="multi-input-container">
                          <div class="multi-input">
                            @if (field.foreginKey) {
                              <!-- Use dropdown-field component for multi-field dropdowns -->
                              @if (field.foreginKey === 'fieldType') {
                                <app-dropdown-field
                                  [field]="field"
                                  [form]="form"
                                  [isViewMode]="isViewMode"
                                  [dropdownType]="'type'"
                                  [fieldSuffix]="j.toString()"
                                  (optionSelected)="onDropdownOptionSelected($event)">
                                </app-dropdown-field>
                              }
                              @else if (field.foreginKey === 'formDefinition') {
                                <app-dropdown-field
                                  [field]="field"
                                  [form]="form"
                                  [isViewMode]="isViewMode"
                                  [dropdownType]="'foreignKey'"
                                  [fieldSuffix]="j.toString()"
                                  (optionSelected)="onDropdownOptionSelected($event)">
                                </app-dropdown-field>
                              }
                              @else {
                                <app-dropdown-field
                                  [field]="field"
                                  [form]="form"
                                  [isViewMode]="isViewMode"
                                  [dropdownType]="'regular'"
                                  [fieldSuffix]="j.toString()"
                                  (optionSelected)="onDropdownOptionSelected($event)">
                                </app-dropdown-field>
                              }
                            } @else {
                              <!-- Regular input fields for non-foreign key multi-fields -->
                              @if (field.type === 'string') {
                                <input [formControlName]="field.fieldName" type="text"
                                  [placeholder]="(field.label?.trim() || field.fieldName) + '-' + (j+1)" [disabled]="field.noInput" />
                              }
                              @if (field.type === 'int') {
                                <input [formControlName]="field.fieldName" type="number" [disabled]="field.noInput" />
                              }
                              @if (field.type === 'boolean') {
                                <input [formControlName]="field.fieldName" type="checkbox" [disabled]="field.noInput" />
                              }
                              @if (field.type === 'date') {
                                <input [formControlName]="field.fieldName" type="date" [disabled]="field.noInput" />
                              }
                              @if (field.type === 'double') {
                                <input [formControlName]="field.fieldName" type="number"
                                  step="00.50" [disabled]="field.noInput" />
                              }
                            }
                          </div>

                          <div class="multi-buttons">
                            @if (getMultiArray(field.fieldName).length > 1 && !isViewMode && !field.noInput) {
                              <button mat-icon-button color="warn" type="button" (click)="removeMultiField(field.fieldName, j)" matTooltip="Delete">
                                <mat-icon>delete</mat-icon>
                              </button>
                            }

                            @if (!isViewMode && !field.noInput) {
                              <button mat-icon-button color="primary" type="button" (click)="addMultiField(field, undefined, j)" matTooltip="Add">
                                <mat-icon>add</mat-icon>
                              </button>
                            }
                          </div>
                        </div>
                      </div>
                    }
                  </div>
                }

              </div>
            }

            <!-- 🔸 Grouped Fields -->
            @if (field.Group && isFirstFieldInParentGroup(field)) {
              @let parsed = parseGroupPath(field.Group);
              @if (parsed.parent) {
                <div [formArrayName]="parsed.parent" class="grouped-field-section">
                  <h3>{{ parsed.parent }}</h3>
                  @for (group of getGroupArray(parsed.parent).controls; track group; let k = $index) {
                    <div [formGroupName]="k" [class]="isRowView ? 'form-grid row-view-table' : 'form-grid multi-field'">

                      @if (isRowView) {
                        <!-- Row View: All fields in a single table-like row -->
                        <div class="row-view-table-container">
                          <!-- Parent group fields -->
                          @for (groupField of getFieldsForGroup(parsed.parent); track groupField.fieldName) {
                            @if (!groupField.isMulti) {
                              <div class="row-view-table-cell">
                                <label>{{ groupField.fieldName }} @if (groupField.mandatory) {<span>*</span>}
                                  @if (groupField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                                </label>
                                @if (groupField.foreginKey) {
                                  <!-- Use dropdown-field component for grouped field dropdowns -->
                                  @if (groupField.foreginKey === 'fieldType') {
                                    <app-dropdown-field
                                      [field]="groupField"
                                      [form]="form"
                                      [isViewMode]="isViewMode"
                                      [dropdownType]="'type'"
                                      [fieldSuffix]="'group_' + k"
                                      (optionSelected)="onDropdownOptionSelected($event)">
                                    </app-dropdown-field>
                                  }
                                  @else if (groupField.foreginKey === 'formDefinition') {
                                    <app-dropdown-field
                                      [field]="groupField"
                                      [form]="form"
                                      [isViewMode]="isViewMode"
                                      [dropdownType]="'foreignKey'"
                                      [fieldSuffix]="'group_' + k"
                                      (optionSelected)="onDropdownOptionSelected($event)">
                                    </app-dropdown-field>
                                  }
                                  @else {
                                    <app-dropdown-field
                                      [field]="groupField"
                                      [form]="form"
                                      [isViewMode]="isViewMode"
                                      [dropdownType]="'regular'"
                                      [fieldSuffix]="'group_' + k"
                                      (optionSelected)="onDropdownOptionSelected($event)">
                                    </app-dropdown-field>
                                  }
                                } @else {
                                  <!-- Regular input fields for non-foreign key fields -->
                                  @if (groupField.type === 'boolean') {
                                    <input [formControlName]="groupField.fieldName" type="checkbox" [disabled]="groupField.noInput" />
                                  }
                                  @if (groupField.type === 'string') {
                                    <input [formControlName]="groupField.fieldName" type="text" [disabled]="groupField.noInput" [placeholder]="(groupField.label?.trim() || groupField.fieldName)" />
                                  }
                                  @if (groupField.type === 'int') {
                                    <input [formControlName]="groupField.fieldName" type="number" [disabled]="groupField.noInput" />
                                  }
                                  @if (groupField.type === 'date') {
                                    <input [formControlName]="groupField.fieldName" type="date" [disabled]="groupField.noInput" />
                                  }
                                  @if (groupField.type === 'double') {
                                    <input [formControlName]="groupField.fieldName" type="number" step="00.50" [disabled]="groupField.noInput" />
                                  }
                                }
                              </div>
                            }
                          }
                        </div>
                      } @else {
                        <!-- Nested View: Fields in their natural groups -->
                        @for (groupField of getFieldsForGroup(parsed.parent); track groupField.fieldName) {
                          @if (!groupField.isMulti) {
                            <div class="form-field">
                              <label>{{ groupField.fieldName }} @if (groupField.mandatory) {<span>*</span>}
                                @if (groupField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                              </label>
                              @if (groupField.foreginKey) {
                                <!-- Similar dropdown structure as above for nested view -->
                                <div class="dropdown-input-container">
                                  <input [formControlName]="groupField.fieldName" type="text"
                                         class="form-input dropdown-input"
                                         [disabled]="groupField.noInput"
                                         [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />
                                </div>
                              } @else {
                                <!-- Regular input fields -->
                                @if (groupField.type === 'string') {
                                  <input [formControlName]="groupField.fieldName" type="text" [disabled]="groupField.noInput" />
                                }
                                @if (groupField.type === 'int') {
                                  <input [formControlName]="groupField.fieldName" type="number" [disabled]="groupField.noInput" />
                                }
                                @if (groupField.type === 'boolean') {
                                  <input [formControlName]="groupField.fieldName" type="checkbox" [disabled]="groupField.noInput" />
                                }
                                @if (groupField.type === 'date') {
                                  <input [formControlName]="groupField.fieldName" type="date" [disabled]="groupField.noInput" />
                                }
                                @if (groupField.type === 'double') {
                                  <input [formControlName]="groupField.fieldName" type="number" step="00.50" [disabled]="groupField.noInput" />
                                }
                              }
                            </div>
                          }
                        }
                      }

                      <!-- Group action buttons -->
                      <div class="button-group">
                        @if (getGroupArray(parsed.parent).length > 1 && !isViewMode) {
                          <button mat-icon-button color="warn" type="button" (click)="removeGroup(parsed.parent, k)" matTooltip="Delete Group">
                            <mat-icon>delete</mat-icon>
                          </button>
                        }

                        @if (!isViewMode) {
                          <button mat-icon-button color="primary" type="button" (click)="addGroup(parsed.parent, k)" matTooltip="Add Group">
                            <mat-icon>add</mat-icon>
                          </button>
                        }

                        @if (!isViewMode) {
                          <button mat-icon-button color="accent" type="button" (click)="cloneGroup(parsed.parent, k)" matTooltip="Clone Group">
                            <mat-icon>content_copy</mat-icon>
                          </button>
                        }
                      </div>
                    </div>
                  }
                </div>
              }
            }

          }
          </ng-container>
        </div>
      </div>
    </form>
    </div>
  }
} @else {
  <div class="success-message">Record submitted successfully!</div>
}
