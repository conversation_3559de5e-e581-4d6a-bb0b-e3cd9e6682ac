import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup } from '@angular/forms';
import { DropdownFieldComponent } from '../dropdown-field/dropdown-field.component';

@Component({
  selector: 'app-field-renderer',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DropdownFieldComponent
  ],
  templateUrl: './field-renderer.component.html',
  styleUrls: ['./field-renderer.component.scss']
})
export class FieldRendererComponent {
  @Input() field: any;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() fieldSuffix: string = '';
  @Input() isMultiField: boolean = false;
  @Input() multiIndex?: number;

  // 🔄 EXACT BACKUP RESTORATION: No debugging in backup version

  getFieldName(): string {
    return this.fieldSuffix ? `${this.field.fieldName}_${this.fieldSuffix}` : this.field.fieldName;
  }

  getPlaceholder(): string {
    if (this.isMultiField && this.multiIndex !== undefined) {
      return `${this.field.label?.trim() || this.field.fieldName}-${this.multiIndex + 1}`;
    }
    return this.field.label?.trim() || this.field.fieldName;
  }

  // 🔄 EXACT BACKUP LOGIC: Check for dropdown fields (foreginKey property)
  isDropdownField(): boolean {
    return !!this.field.foreginKey;
  }

  getDropdownType(): 'type' | 'foreignKey' | 'regular' {
    if (this.field.foreginKey === 'fieldType') {
      return 'type';
    } else if (this.field.foreginKey === 'formDefinition') {
      return 'foreignKey';
    } else {
      return 'regular';
    }
  }

  onDropdownOptionSelected(event: {option: any, fieldName: string}): void {
    // This will be handled by the parent component
    // The dropdown component already sets the form value
  }
}