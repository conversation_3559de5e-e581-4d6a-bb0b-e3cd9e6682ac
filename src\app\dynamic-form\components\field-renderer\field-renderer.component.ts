import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-field-renderer',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './field-renderer.component.html',
  styleUrls: ['./field-renderer.component.scss']
})
export class FieldRendererComponent {
  @Input() field: any;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() showTypeDropdown: { [key: string]: boolean } = {};
  @Input() showForeignKeyDropdown: { [key: string]: boolean } = {};
  @Input() showRegularDropdown: { [key: string]: boolean } = {};
  @Input() filteredTypeOptions: { [key: string]: any[] } = {};
  @Input() filteredForeignKeyOptions: { [key: string]: any[] } = {};
  @Input() filteredRegularOptions: { [key: string]: any[] } = {};
  @Input() fieldSuffix: string = '';
  
  @Output() typeInputChange = new EventEmitter<{event: Event, fieldName: string}>();
  @Output() typeInputFocus = new EventEmitter<string>();
  @Output() typeInputBlur = new EventEmitter<string>();
  @Output() toggleTypeDropdown = new EventEmitter<string>();
  @Output() selectTypeOption = new EventEmitter<{option: any, fieldName: string}>();
  
  @Output() foreignKeyInputChange = new EventEmitter<{event: Event, fieldName: string}>();
  @Output() foreignKeyInputFocus = new EventEmitter<string>();
  @Output() foreignKeyInputBlur = new EventEmitter<string>();
  @Output() toggleForeignKeyDropdown = new EventEmitter<string>();
  @Output() selectForeignKeyOption = new EventEmitter<{option: any, fieldName: string}>();
  
  @Output() regularInputChange = new EventEmitter<{event: Event, fieldName: string}>();
  @Output() regularInputFocus = new EventEmitter<string>();
  @Output() regularInputBlur = new EventEmitter<string>();
  @Output() toggleRegularDropdown = new EventEmitter<string>();
  @Output() selectRegularOption = new EventEmitter<{option: any, fieldName: string}>();

  getFieldName(): string {
    return this.fieldSuffix ? `${this.field.fieldName}_${this.fieldSuffix}` : this.field.fieldName;
  }

  getKeys(option: any): string[] {
    return Object.keys(option);
  }

  isTypeField(): boolean {
    return this.field.foreginKey === 'fieldType';
  }

  isForeignKeyField(): boolean {
    return this.field.foreginKey === 'formDefinition';
  }

  isRegularForeignKeyField(): boolean {
    return this.field.foreginKey && !this.isTypeField() && !this.isForeignKeyField();
  }

  // Type field methods
  onTypeInputChange(event: Event): void {
    this.typeInputChange.emit({event, fieldName: this.getFieldName()});
  }

  onTypeInputFocus(): void {
    this.typeInputFocus.emit(this.getFieldName());
  }

  onTypeInputBlur(): void {
    this.typeInputBlur.emit(this.getFieldName());
  }

  onToggleTypeDropdown(): void {
    this.toggleTypeDropdown.emit(this.getFieldName());
  }

  onSelectTypeOption(option: any): void {
    this.selectTypeOption.emit({option, fieldName: this.getFieldName()});
  }

  // Foreign key field methods
  onForeignKeyInputChange(event: Event): void {
    this.foreignKeyInputChange.emit({event, fieldName: this.getFieldName()});
  }

  onForeignKeyInputFocus(): void {
    this.foreignKeyInputFocus.emit(this.getFieldName());
  }

  onForeignKeyInputBlur(): void {
    this.foreignKeyInputBlur.emit(this.getFieldName());
  }

  onToggleForeignKeyDropdown(): void {
    this.toggleForeignKeyDropdown.emit(this.getFieldName());
  }

  onSelectForeignKeyOption(option: any): void {
    this.selectForeignKeyOption.emit({option, fieldName: this.getFieldName()});
  }

  // Regular foreign key field methods
  onRegularInputChange(event: Event): void {
    this.regularInputChange.emit({event, fieldName: this.getFieldName()});
  }

  onRegularInputFocus(): void {
    this.regularInputFocus.emit(this.getFieldName());
  }

  onRegularInputBlur(): void {
    this.regularInputBlur.emit(this.getFieldName());
  }

  onToggleRegularDropdown(): void {
    this.toggleRegularDropdown.emit(this.getFieldName());
  }

  onSelectRegularOption(option: any): void {
    this.selectRegularOption.emit({option, fieldName: this.getFieldName()});
  }
} 