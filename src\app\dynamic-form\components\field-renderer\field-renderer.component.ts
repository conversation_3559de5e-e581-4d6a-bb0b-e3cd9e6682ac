import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup } from '@angular/forms';
import { DropdownFieldComponent } from '../dropdown-field/dropdown-field.component';

@Component({
  selector: 'app-field-renderer',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DropdownFieldComponent
  ],
  templateUrl: './field-renderer.component.html',
  styleUrls: ['./field-renderer.component.scss']
})
export class FieldRendererComponent implements OnInit {
  @Input() field: any;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() fieldSuffix: string = '';
  @Input() isMultiField: boolean = false;
  @Input() multiIndex?: number;

  ngOnInit() {
    // 🔍 Debug: Track field rendering
    if (!this.field.Group) {
      console.log(`🎨 FIELD-RENDERER: Rendering ungrouped field "${this.field.fieldName}" with type "${this.field.type || 'undefined'}"`);
    }
  }

  getFieldName(): string {
    return this.fieldSuffix ? `${this.field.fieldName}_${this.fieldSuffix}` : this.field.fieldName;
  }

  getPlaceholder(): string {
    if (this.isMultiField && this.multiIndex !== undefined) {
      return `${this.field.label?.trim() || this.field.fieldName}-${this.multiIndex + 1}`;
    }
    return this.field.label?.trim() || this.field.fieldName;
  }

  isDropdownField(): boolean {
    return !!this.field.foreginKey;
  }

  getDropdownType(): 'type' | 'foreignKey' | 'regular' {
    if (this.field.foreginKey === 'fieldType') {
      return 'type';
    } else if (this.field.foreginKey === 'formDefinition') {
      return 'foreignKey';
    } else {
      return 'regular';
    }
  }

  onDropdownOptionSelected(event: {option: any, fieldName: string}): void {
    // This will be handled by the parent component
    // The dropdown component already sets the form value
  }
}