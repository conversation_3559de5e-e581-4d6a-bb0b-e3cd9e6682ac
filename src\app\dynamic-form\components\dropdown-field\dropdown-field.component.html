<div class="dropdown-input-container">
  <input [formControlName]="getFieldName()" [id]="getFieldName()" type="text" 
         class="form-input dropdown-input" 
         (input)="onInputChange($event)" 
         (focus)="onInputFocus()" 
         (blur)="onInputBlur()"
         [disabled]="isViewMode || field.noInput"
         [placeholder]="getPlaceholder()" />
  
  <!-- Arrow button to toggle dropdown -->
  <button type="button" class="dropdown-arrow-btn" 
          (click)="onToggleDropdown()" 
          [disabled]="isViewMode || field.noInput"
          [matTooltip]="getTooltipText()">
    <mat-icon>{{ showDropdown ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
  </button>
  
  <!-- Dropdown list for filtered results -->
  @if (showDropdown) {
    <div class="dropdown-list">
      @if (filteredOptions && filteredOptions.length > 0) {
        @for (option of filteredOptions; track option.ROW_ID) {
          <div class="dropdown-item" (click)="onSelectOption(option)">
            @if (dropdownType === 'regular') {
              @for (key of getKeys(option); track key) {
                {{ option[key] }}&nbsp;
              }
            } @else {
              {{ option.ROW_ID }}
            }
          </div>
        }
      } @else {
        <div class="dropdown-empty">
          @if (dropdownType === 'type') {
            No types found
          } @else if (dropdownType === 'foreignKey') {
            No foreign keys found
          } @else {
            No options found
          }
        </div>
      }
    </div>
  }
</div> 