<div class="nested-group-section">
  @for (field of nestedGroupFields; track field.fieldName) {

    <!-- 🔸 Regular Field in Nested Group -->
    @if (!field.isMulti) {
      <div class="nested-field">
        <!-- Use field-renderer component for nested fields -->
        <app-field-renderer
          [field]="field"
          [form]="formGroup"
          [isViewMode]="isViewMode"
          [fieldSuffix]="getFieldName(field.fieldName)">
        </app-field-renderer>
      </div>
    }

    <!-- 🔸 Multi-Field in Nested Group -->
    @if (field.isMulti) {
      <div class="nested-multi-field">
        <!-- Use multi-field component for nested multi-fields -->
        <app-multi-field
          [field]="field"
          [form]="formGroup"
          [isViewMode]="isViewMode"
          (multiFieldAdded)="onMultiFieldAdded($event)"
          (multiFieldRemoved)="onMultiFieldRemoved($event)">
        </app-multi-field>
      </div>
    }

  }
</div>