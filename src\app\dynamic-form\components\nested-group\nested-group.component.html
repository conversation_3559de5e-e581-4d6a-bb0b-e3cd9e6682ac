<div class="nested-group-section">
  <ng-container *ngFor="let field of nestedGroupFields">
    
    <!-- 🔸 Regular Field in Nested Group -->
    @if (!field.isMulti) {
      <div class="nested-field">
        <label>{{ field.fieldName }} @if (field.mandatory) {<span>*</span>}
          @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
        </label>
        @if (field.foreginKey) {
          <!-- Check if this is a type field (fieldType) -->
          @if (field.foreginKey === 'fieldType') {
            <div class="dropdown-input-container">
              <input [formControlName]="field.fieldName" type="text" 
                     class="form-input dropdown-input" 
                     (input)="typeInputChange.emit({event: $event, fieldName: getFieldName(field.fieldName)})" 
                     (focus)="typeInputFocus.emit(getFieldName(field.fieldName))" 
                     (blur)="typeInputBlur.emit(getFieldName(field.fieldName))"
                     [disabled]="field.noInput"
                     [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
              
              <button type="button" class="dropdown-arrow-btn" 
                      (click)="toggleTypeDropdown.emit(getFieldName(field.fieldName))" 
                      [disabled]="field.noInput"
                      matTooltip="Show type suggestions">
                <mat-icon>{{ showTypeDropdown[getFieldName(field.fieldName)] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
              </button>
              
              @if (showTypeDropdown[getFieldName(field.fieldName)]) {
                <div class="dropdown-list">
                  @if (filteredTypeOptions[getFieldName(field.fieldName)] && filteredTypeOptions[getFieldName(field.fieldName)].length > 0) {
                    @for (option of filteredTypeOptions[getFieldName(field.fieldName)]; track option.ROW_ID) {
                      <div class="dropdown-item" (click)="selectTypeOption.emit({option: option, fieldName: getFieldName(field.fieldName)})">
                        {{ option.ROW_ID }}
                      </div>
                    }
                  } @else {
                    <div class="dropdown-empty">
                      No types found
                    </div>
                  }
                </div>
              }
            </div>
          }
          <!-- Check if this is a foreign key field (formDefinition) -->
          @else if (field.foreginKey === 'formDefinition') {
            <div class="dropdown-input-container">
              <input [formControlName]="field.fieldName" type="text" 
                     class="form-input dropdown-input" 
                     (input)="foreignKeyInputChange.emit({event: $event, fieldName: getFieldName(field.fieldName)})" 
                     (focus)="foreignKeyInputFocus.emit(getFieldName(field.fieldName))" 
                     (blur)="foreignKeyInputBlur.emit(getFieldName(field.fieldName))"
                     [disabled]="field.noInput"
                     [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
              
              <button type="button" class="dropdown-arrow-btn" 
                      (click)="toggleForeignKeyDropdown.emit(getFieldName(field.fieldName))" 
                      [disabled]="field.noInput"
                      matTooltip="Show foreign key suggestions">
                <mat-icon>{{ showForeignKeyDropdown[getFieldName(field.fieldName)] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
              </button>
              
              @if (showForeignKeyDropdown[getFieldName(field.fieldName)]) {
                <div class="dropdown-list">
                  @if (filteredForeignKeyOptions[getFieldName(field.fieldName)] && filteredForeignKeyOptions[getFieldName(field.fieldName)].length > 0) {
                    @for (option of filteredForeignKeyOptions[getFieldName(field.fieldName)]; track option.ROW_ID) {
                      <div class="dropdown-item" (click)="selectForeignKeyOption.emit({option: option, fieldName: getFieldName(field.fieldName)})">
                        {{ option.ROW_ID }}
                      </div>
                    }
                  } @else {
                    <div class="dropdown-empty">
                      No foreign keys found
                    </div>
                  }
                </div>
              }
            </div>
          }
          <!-- Default dropdown for other foreign keys -->
          @else {
            <div class="dropdown-input-container">
              <input [formControlName]="field.fieldName" type="text" 
                     class="form-input dropdown-input" 
                     (input)="regularInputChange.emit({event: $event, fieldName: getFieldName(field.fieldName)})" 
                     (focus)="regularInputFocus.emit(getFieldName(field.fieldName))" 
                     (blur)="regularInputBlur.emit(getFieldName(field.fieldName))"
                     [disabled]="field.noInput"
                     [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
              
              <button type="button" class="dropdown-arrow-btn" 
                      (click)="toggleRegularDropdown.emit(getFieldName(field.fieldName))" 
                      [disabled]="field.noInput"
                      matTooltip="Show options">
                <mat-icon>{{ showRegularDropdown[getFieldName(field.fieldName)] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
              </button>
              
              @if (showRegularDropdown[getFieldName(field.fieldName)]) {
                <div class="dropdown-list">
                  @if (filteredRegularOptions[getFieldName(field.fieldName)] && filteredRegularOptions[getFieldName(field.fieldName)].length > 0) {
                    @for (option of filteredRegularOptions[getFieldName(field.fieldName)]; track option.ROW_ID) {
                      <div class="dropdown-item" (click)="selectRegularOption.emit({option: option, fieldName: getFieldName(field.fieldName)})">
                        @for (key of getKeys(option); track key) {
                          {{ option[key] }}&nbsp;
                        }
                      </div>
                    }
                  } @else {
                    <div class="dropdown-empty">
                      No options found
                    </div>
                  }
                </div>
              }
            </div>
          }
        } @else {
          <!-- Regular input fields for non-foreign key nested fields -->
          @if (field.type === 'string') {
            <input [formControlName]="field.fieldName" type="text" [placeholder]="(field.label?.trim() || field.fieldName)" [disabled]="field.noInput" />
          }
          @if (field.type === 'int') {
            <input [formControlName]="field.fieldName" type="number" [disabled]="field.noInput" />
          }
          @if (field.type === 'boolean') {
            <input [formControlName]="field.fieldName" type="checkbox" [disabled]="field.noInput" />
          }
          @if (field.type === 'date') {
            <input [formControlName]="field.fieldName" type="date" [disabled]="field.noInput" />
          }
          @if (field.type === 'double') {
            <input [formControlName]="field.fieldName" type="number" step="00.50" [disabled]="field.noInput" />
          }
        }
      </div>
    }

    <!-- 🔸 Multi-Field in Nested Group -->
    @if (field.isMulti) {
      <div class="nested-multi-field">
        <div [formArrayName]="field.fieldName">
          @for (control of getMultiArray(field.fieldName, groupIndex, parentGroupName).controls; track control; let j = $index) {
            <div [formGroupName]="j" class="form-field is-multi">
              <label>{{ field.fieldName }} ({{ j + 1 }})
                @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
              </label>

              <div class="multi-input-container">
                <div class="multi-input">
                  @if (field.foreginKey) {
                    <!-- Check if this is a type field (fieldType) -->
                    @if (field.foreginKey === 'fieldType') {
                      <div class="dropdown-input-container">
                        <input [formControlName]="field.fieldName" type="text" 
                               class="form-input dropdown-input" 
                               (input)="typeInputChange.emit({event: $event, fieldName: getMultiFieldName(field.fieldName, j)})" 
                               (focus)="typeInputFocus.emit(getMultiFieldName(field.fieldName, j))" 
                               (blur)="typeInputBlur.emit(getMultiFieldName(field.fieldName, j))"
                               [disabled]="field.noInput"
                               [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
                        
                        <button type="button" class="dropdown-arrow-btn" 
                                (click)="toggleTypeDropdown.emit(getMultiFieldName(field.fieldName, j))" 
                                [disabled]="field.noInput"
                                matTooltip="Show type suggestions">
                          <mat-icon>{{ showTypeDropdown[getMultiFieldName(field.fieldName, j)] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                        </button>
                        
                        @if (showTypeDropdown[getMultiFieldName(field.fieldName, j)]) {
                          <div class="dropdown-list">
                            @if (filteredTypeOptions[getMultiFieldName(field.fieldName, j)] && filteredTypeOptions[getMultiFieldName(field.fieldName, j)].length > 0) {
                              @for (option of filteredTypeOptions[getMultiFieldName(field.fieldName, j)]; track option.ROW_ID) {
                                <div class="dropdown-item" (click)="selectTypeOption.emit({option: option, fieldName: getMultiFieldName(field.fieldName, j)})">
                                  {{ option.ROW_ID }}
                                </div>
                              }
                            } @else {
                              <div class="dropdown-empty">
                                No types found
                              </div>
                            }
                          </div>
                        }
                      </div>
                    }
                    <!-- Check if this is a foreign key field (formDefinition) -->
                    @else if (field.foreginKey === 'formDefinition') {
                      <div class="dropdown-input-container">
                        <input [formControlName]="field.fieldName" type="text" 
                               class="form-input dropdown-input" 
                               (input)="foreignKeyInputChange.emit({event: $event, fieldName: getMultiFieldName(field.fieldName, j)})" 
                               (focus)="foreignKeyInputFocus.emit(getMultiFieldName(field.fieldName, j))" 
                               (blur)="foreignKeyInputBlur.emit(getMultiFieldName(field.fieldName, j))"
                               [disabled]="field.noInput"
                               [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
                        
                        <button type="button" class="dropdown-arrow-btn" 
                                (click)="toggleForeignKeyDropdown.emit(getMultiFieldName(field.fieldName, j))" 
                                [disabled]="field.noInput"
                                matTooltip="Show foreign key suggestions">
                          <mat-icon>{{ showForeignKeyDropdown[getMultiFieldName(field.fieldName, j)] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                        </button>
                        
                        @if (showForeignKeyDropdown[getMultiFieldName(field.fieldName, j)]) {
                          <div class="dropdown-list">
                            @if (filteredForeignKeyOptions[getMultiFieldName(field.fieldName, j)] && filteredForeignKeyOptions[getMultiFieldName(field.fieldName, j)].length > 0) {
                              @for (option of filteredForeignKeyOptions[getMultiFieldName(field.fieldName, j)]; track option.ROW_ID) {
                                <div class="dropdown-item" (click)="selectForeignKeyOption.emit({option: option, fieldName: getMultiFieldName(field.fieldName, j)})">
                                  {{ option.ROW_ID }}
                                </div>
                              }
                            } @else {
                              <div class="dropdown-empty">
                                No foreign keys found
                              </div>
                            }
                          </div>
                        }
                      </div>
                    }
                    <!-- Default dropdown for other foreign keys -->
                    @else {
                      <div class="dropdown-input-container">
                        <input [formControlName]="field.fieldName" type="text" 
                               class="form-input dropdown-input" 
                               (input)="regularInputChange.emit({event: $event, fieldName: getMultiFieldName(field.fieldName, j)})" 
                               (focus)="regularInputFocus.emit(getMultiFieldName(field.fieldName, j))" 
                               (blur)="regularInputBlur.emit(getMultiFieldName(field.fieldName, j))"
                               [disabled]="field.noInput"
                               [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
                        
                        <button type="button" class="dropdown-arrow-btn" 
                                (click)="toggleRegularDropdown.emit(getMultiFieldName(field.fieldName, j))" 
                                [disabled]="field.noInput"
                                matTooltip="Show options">
                          <mat-icon>{{ showRegularDropdown[getMultiFieldName(field.fieldName, j)] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                        </button>
                        
                        @if (showRegularDropdown[getMultiFieldName(field.fieldName, j)]) {
                          <div class="dropdown-list">
                            @if (filteredRegularOptions[getMultiFieldName(field.fieldName, j)] && filteredRegularOptions[getMultiFieldName(field.fieldName, j)].length > 0) {
                              @for (option of filteredRegularOptions[getMultiFieldName(field.fieldName, j)]; track option.ROW_ID) {
                                <div class="dropdown-item" (click)="selectRegularOption.emit({option: option, fieldName: getMultiFieldName(field.fieldName, j)})">
                                  @for (key of getKeys(option); track key) {
                                    {{ option[key] }}&nbsp;
                                  }
                                </div>
                              }
                            } @else {
                              <div class="dropdown-empty">
                                No options found
                              </div>
                            }
                          </div>
                        }
                      </div>
                    }
                  } @else {
                    <!-- Regular input fields for non-foreign key multi-fields -->
                    @if (field.type === 'string') {
                      <input [formControlName]="field.fieldName" type="text"
                        [placeholder]="(field.label?.trim() || field.fieldName) + '-' + (j+1)" [disabled]="field.noInput" />
                    }
                    @if (field.type === 'int') {
                      <input [formControlName]="field.fieldName" type="number" [disabled]="field.noInput" />
                    }
                    @if (field.type === 'boolean') {
                      <input [formControlName]="field.fieldName" type="checkbox" [disabled]="field.noInput" />
                    }
                    @if (field.type === 'date') {
                      <input [formControlName]="field.fieldName" type="date" [disabled]="field.noInput" />
                    }
                    @if (field.type === 'double') {
                      <input [formControlName]="field.fieldName" type="number"
                        step="00.50" [disabled]="field.noInput" />
                    }
                  }
                </div>

                <div class="multi-buttons">
                  @if (getMultiArray(field.fieldName, groupIndex, parentGroupName).length > 1 && !isViewMode && !field.noInput) {
                    <button mat-icon-button color="warn" type="button" (click)="removeMultiField.emit({fieldName: field.fieldName, index: j, groupIndex: groupIndex, parentGroupName: parentGroupName})" matTooltip="Delete">
                      <mat-icon>delete</mat-icon>
                    </button>
                  }

                  @if (!isViewMode && !field.noInput) {
                    <button mat-icon-button color="primary" type="button" (click)="addMultiField.emit({field: field, index: j, groupIndex: groupIndex, parentGroupName: parentGroupName})" matTooltip="Add">
                      <mat-icon>add</mat-icon>
                    </button>
                  }
                </div>
              </div>
            </div>
          }
        </div>
      </div>
    }

  </ng-container>
</div> 