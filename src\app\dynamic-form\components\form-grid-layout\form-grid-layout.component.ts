import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup } from '@angular/forms';
import { FieldRendererComponent } from '../field-renderer/field-renderer.component';
import { MultiFieldComponent } from '../multi-field/multi-field.component';
import { GroupFieldComponent } from '../group-field/group-field.component';

@Component({
  selector: 'app-form-grid-layout',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FieldRendererComponent,
    MultiFieldComponent,
    GroupFieldComponent
  ],
  templateUrl: './form-grid-layout.component.html',
  styleUrls: ['./form-grid-layout.component.scss']
})
export class FormGridLayoutComponent implements OnInit {
  @Input() columns: any[][] = [];
  @Input() columnCount: number = 1;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() isRowView: boolean = false;
  @Input() fields: any[] = [];

  ngOnInit() {
    // 🔍 Debug: Track grid layout rendering
    const totalFields = this.columns.reduce((total, column) => total + column.length, 0);
    const ungroupedFields = this.columns.reduce((total, column) =>
      total + column.filter(field => !field.Group && field.fieldName?.toUpperCase() !== 'ID').length, 0);
    const groupedFields = this.columns.reduce((total, column) =>
      total + column.filter(field => field.Group).length, 0);

    console.log(`🏗️ GRID-LAYOUT: Rendering ${totalFields} total fields in ${this.columnCount} columns`);
    console.log(`📋 GRID-LAYOUT: ${ungroupedFields} ungrouped fields, ${groupedFields} grouped fields`);
  }

  @Output() multiFieldAdded = new EventEmitter<{field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number}>();
  @Output() multiFieldRemoved = new EventEmitter<{fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number}>();
  @Output() groupAdded = new EventEmitter<{groupName: string, index: number}>();
  @Output() groupRemoved = new EventEmitter<{groupName: string, index: number}>();
  @Output() groupCloned = new EventEmitter<{groupName: string, index: number}>();

  parseGroupPath(groupPath: string): { parent: string | null, child: string | null, isNested: boolean } {
    const trimmedGroupPath = groupPath.trim();
    if (trimmedGroupPath.includes('|')) {
      const parts = trimmedGroupPath.split('|');
      return {
        parent: parts[0].trim(),
        child: parts[1].trim(),
        isNested: true
      };
    }
    return {
      parent: trimmedGroupPath.trim(),
      child: null,
      isNested: false
    };
  }

  isFirstFieldInParentGroup(field: any): boolean {
    if (!field.Group) return false;
    const parsed = this.parseGroupPath(field.Group);
    if (!parsed.parent) return false;

    // Find the first field in this group
    const fieldsInGroup = this.fields.filter(f => {
      if (!f.Group) return false;
      const fieldParsed = this.parseGroupPath(f.Group);
      return fieldParsed.parent === parsed.parent;
    });

    return fieldsInGroup.length > 0 && fieldsInGroup[0].fieldName === field.fieldName;
  }

  onMultiFieldAdded(event: any): void {
    this.multiFieldAdded.emit(event);
  }

  onMultiFieldRemoved(event: any): void {
    this.multiFieldRemoved.emit(event);
  }

  onGroupAdded(event: any): void {
    this.groupAdded.emit(event);
  }

  onGroupRemoved(event: any): void {
    this.groupRemoved.emit(event);
  }

  onGroupCloned(event: any): void {
    this.groupCloned.emit(event);
  }

  /**
   * Static method to distribute fields into columns using round-robin algorithm
   * This handles both grouped and non-grouped fields appropriately
   */
  static distributeFieldsRoundRobin(fields: any[], columnCount: number): any[][] {
    const columns: any[][] = Array.from({ length: columnCount }, () => []);

    // Group fields by their group name
    const groupedFields: { [key: string]: any[] } = {};
    const nonGroupedFields: any[] = [];

    fields.forEach(field => {
      if (field.Group) {
        if (!groupedFields[field.Group]) {
          groupedFields[field.Group] = [];
        }
        groupedFields[field.Group].push(field);
      } else {
        nonGroupedFields.push(field);
      }
    });

    // Distribute non-grouped fields using round-robin
    nonGroupedFields.forEach((field, index) => {
      const colIndex = index % columnCount;
      columns[colIndex].push(field);
    });

    // Add only the first field of each group to the first column
    // This ensures grouped fields appear together and don't get split across columns
    Object.keys(groupedFields).forEach(groupName => {
      if (groupedFields[groupName].length > 0) {
        columns[0].push(groupedFields[groupName][0]);
      }
    });

    return columns;
  }

  /**
   * Calculate responsive column count based on screen size and field count
   */
  static calculateResponsiveColumns(fieldCount: number, maxColumns: number = 3): number {
    if (fieldCount <= 3) return 1;
    if (fieldCount <= 6) return 2;
    return Math.min(maxColumns, Math.ceil(fieldCount / 3));
  }
}