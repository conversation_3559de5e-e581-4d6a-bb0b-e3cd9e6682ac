import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MultiFieldComponent } from '../multi-field/multi-field.component';
import { GroupFieldComponent } from '../group-field/group-field.component';

@Component({
  selector: 'app-form-grid-layout',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MultiFieldComponent,
    GroupFieldComponent
  ],
  templateUrl: './form-grid-layout.component.html',
  styleUrls: ['./form-grid-layout.component.scss']
})
export class FormGridLayoutComponent {
  @Input() columns: any[][] = [];
  @Input() columnCount: number = 1;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() isRowView: boolean = false;
  @Input() showTypeDropdown: { [key: string]: boolean } = {};
  @Input() showForeignKeyDropdown: { [key: string]: boolean } = {};
  @Input() showRegularDropdown: { [key: string]: boolean } = {};
  @Input() filteredTypeOptions: { [key: string]: any[] } = {};
  @Input() filteredForeignKeyOptions: { [key: string]: any[] } = {};
  @Input() filteredRegularOptions: { [key: string]: any[] } = {};
  @Input() getMultiArray!: (fieldName: string, groupIndex?: number, groupName?: string, nestedGroupIndex?: number) => any;
  @Input() getFieldsForGroup!: (groupName: string) => any[];
  @Input() getGroupArray!: (groupName: string) => any;
  @Input() parseGroupPath!: (groupPath: string) => any;
  @Input() isFirstFieldInParentGroup!: (field: any) => boolean;

  @Output() addMultiField = new EventEmitter<{field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number}>();
  @Output() removeMultiField = new EventEmitter<number | {fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number}>();
  @Output() addGroup = new EventEmitter<{groupName: string, index: number}>();
  @Output() removeGroup = new EventEmitter<{groupName: string, index: number}>();
  @Output() cloneGroup = new EventEmitter<{groupName: string, index: number}>();
  @Output() typeInputChange = new EventEmitter<{event: Event, fieldName: string}>();
  @Output() typeInputFocus = new EventEmitter<string>();
  @Output() typeInputBlur = new EventEmitter<string>();
  @Output() toggleTypeDropdown = new EventEmitter<string>();
  @Output() selectTypeOption = new EventEmitter<{option: any, fieldName: string}>();
  @Output() foreignKeyInputChange = new EventEmitter<{event: Event, fieldName: string}>();
  @Output() foreignKeyInputFocus = new EventEmitter<string>();
  @Output() foreignKeyInputBlur = new EventEmitter<string>();
  @Output() toggleForeignKeyDropdown = new EventEmitter<string>();
  @Output() selectForeignKeyOption = new EventEmitter<{option: any, fieldName: string}>();
  @Output() regularInputChange = new EventEmitter<{event: Event, fieldName: string}>();
  @Output() regularInputFocus = new EventEmitter<string>();
  @Output() regularInputBlur = new EventEmitter<string>();
  @Output() toggleRegularDropdown = new EventEmitter<string>();
  @Output() selectRegularOption = new EventEmitter<{option: any, fieldName: string}>();

  getKeys(option: any): string[] {
    return Object.keys(option);
  }

  handleMultiFieldRemove(index: number, fieldName: string): void {
    this.removeMultiField.emit({fieldName, index});
  }
} 