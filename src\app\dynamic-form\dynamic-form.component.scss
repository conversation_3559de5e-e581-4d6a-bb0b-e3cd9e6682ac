@use '../../styles/shared.scss' as *;

body {
  font-family: 'Poppins', sans-serif;
  color: #333;
  background-color: white !important;
}

/* Main form grid layout */
.form-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  background-color: white;
}

/* Success message styling */
.success-message {
  color: #28a745;
  font-size: 14px;
  font-weight: bold;
  padding: 10px;
  border: 1px solid #c3e6cb;
  background-color: #d4edda;
  border-radius: 8px;
}

/* Error message styling */
.error-message {
  background-color: #fdd;
  border: 1px solid #faa;
  color: #a00;
  padding: 10px;
  margin-bottom: 10px; 
  border-radius: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-grid {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .form-grid {
    padding: 8px;
  }
  
  .success-message {
    font-size: 12px;
    padding: 8px;
  }
  
  .error-message {
    font-size: 12px;
    padding: 8px;
  }
} 