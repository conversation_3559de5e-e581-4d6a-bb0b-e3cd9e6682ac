<form [formGroup]="form" class="dynamic-form-container">
  
  <!-- 🔸 ID Field Section -->
  @if (idField && isIdFieldVisible) {
    <app-initial-input
      [idField]="idField"
      [form]="form"
      [isViewMode]="isViewMode"
    ></app-initial-input>
  }

  <!-- 🔹 All Fields in Order (Unified Rendering) -->
  <app-form-grid-layout
    [columns]="columns"
    [columnCount]="columnCount"
    [form]="form"
    [isViewMode]="isViewMode"
    [isRowView]="isRowView"
    [showTypeDropdown]="showTypeDropdown"
    [showForeignKeyDropdown]="showForeignKeyDropdown"
    [showRegularDropdown]="showRegularDropdown"
    [filteredTypeOptions]="filteredTypeOptions"
    [filteredForeignKeyOptions]="filteredForeignKeyOptions"
    [filteredRegularOptions]="filteredRegularOptions"
    [getMultiArray]="getMultiArray"
    [getFieldsForGroup]="getFieldsForGroup"
    [getGroupArray]="getGroupArray"
    [parseGroupPath]="parseGroupPath"
    [isFirstFieldInParentGroup]="isFirstFieldInParentGroup"
    (addMultiField)="addMultiField.emit($event)"
    (removeMultiField)="removeMultiField.emit($event)"
    (addGroup)="addGroup.emit($event)"
    (removeGroup)="removeGroup.emit($event)"
    (cloneGroup)="cloneGroup.emit($event)"
    (typeInputChange)="typeInputChange.emit($event)"
    (typeInputFocus)="typeInputFocus.emit($event)"
    (typeInputBlur)="typeInputBlur.emit($event)"
    (toggleTypeDropdown)="toggleTypeDropdown.emit($event)"
    (selectTypeOption)="selectTypeOption.emit($event)"
    (foreignKeyInputChange)="foreignKeyInputChange.emit($event)"
    (foreignKeyInputFocus)="foreignKeyInputFocus.emit($event)"
    (foreignKeyInputBlur)="foreignKeyInputBlur.emit($event)"
    (toggleForeignKeyDropdown)="toggleForeignKeyDropdown.emit($event)"
    (selectForeignKeyOption)="selectForeignKeyOption.emit($event)"
    (regularInputChange)="regularInputChange.emit($event)"
    (regularInputFocus)="regularInputFocus.emit($event)"
    (regularInputBlur)="regularInputBlur.emit($event)"
    (toggleRegularDropdown)="toggleRegularDropdown.emit($event)"
    (selectRegularOption)="selectRegularOption.emit($event)">
  </app-form-grid-layout>
</form> 