<form [formGroup]="form" class="dynamic-form-container">

  <!-- 🔸 ID Field Section -->
  @if (idField && isIdFieldVisible) {
    <app-initial-input
      [idField]="idField"
      [form]="form"
      [isViewMode]="isViewMode">
    </app-initial-input>
  }

  <!-- 🔹 All Fields in Order (Unified Rendering) -->
  <app-form-grid-layout
    [columns]="columns"
    [columnCount]="columnCount"
    [form]="form"
    [isViewMode]="isViewMode"
    [isRowView]="isRowView"
    [fields]="fields"
    (multiFieldAdded)="onMultiFieldAdded($event)"
    (multiFieldRemoved)="onMultiFieldRemoved($event)"
    (groupAdded)="onGroupAdded($event)"
    (groupRemoved)="onGroupRemoved($event)"
    (groupCloned)="onGroupCloned($event)">
  </app-form-grid-layout>
</form>