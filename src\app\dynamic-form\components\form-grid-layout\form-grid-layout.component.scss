.form-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  background-color: white;
}

.form-column {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-field {
  width: 100%;
  margin-bottom: 16px;
  position: relative;
}

.form-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 12px;
  color: #000000;
}

.form-field input,
.form-field select {
  width: 100%;
  max-width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #ced4da;
  border-radius: 8px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  box-sizing: border-box;
}

.form-field input:focus,
.form-field select:focus {
  border-color: #9e9e9e;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 12px;
  color: #000000;
}

h3 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #444;
}

.no-input-indicator {
  color: #e74c3c;
  font-size: 11px;
  font-weight: normal;
  font-style: italic;
  margin-left: 4px;
}

.multi-field, .group-fields {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex-wrap: wrap;
  background-color: white;
  margin-bottom: 16px;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  position: relative;
}

.is-multi .multi-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.is-multi .multi-input {
  flex: 1;
  min-width: 0;
}

.is-multi .multi-buttons {
  display: flex;
  flex-direction: row;
  gap: 4px;
  flex-shrink: 0;
  align-items: center;
}

.grouped-field-section {
  width: 100%;
  margin-bottom: 24px;
  padding: 16px;
  background-color: white;
  border-radius: 4px;
  box-sizing: border-box;
  border-left: 4px solid #007bff;
}

.grouped-field-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
}

.dropdown-input-container {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  min-width: 200px;
}

.dropdown-input-container .dropdown-arrow-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-left: none;
  border-radius: 0 8px 8px 0;
  background-color: #f8f9fa;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
}

.dropdown-arrow-btn:hover {
  background-color: #e9ecef;
  color: #283A97;
}

.dropdown-arrow-btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

.dropdown-arrow-btn .mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  line-height: 1;
}

.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: white;
  border: 1px solid #DBDBDB;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

.dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #495057;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #283A97;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-empty {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
}

.row-view-table {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
}

.row-view-table-container {
  width: 100%;
  overflow-x: auto;
  padding: 16px;
  background-color: white;
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.row-view-table-cell {
  flex: 1;
  min-width: 150px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.row-view-table-cell label {
  font-size: 12px;
  font-weight: 500;
  color: #000000;
  margin-bottom: 4px;
}

.row-view-table-cell input,
.row-view-table-cell select {
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #ced4da;
  border-radius: 8px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  box-sizing: border-box;
}

.row-view-table-cell input:focus,
.row-view-table-cell select:focus {
  border-color: #9e9e9e;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

.group-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding: 8px;
  border-top: 1px solid #e0e0e0;
  justify-content: flex-end;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .form-grid {
    padding: 12px;
  }
  .form-field input,
  .form-field select {
    font-size: 16px;
    padding: 10px 12px;
  }
  .no-input-indicator {
    font-size: 10px;
    margin-left: 2px;
  }
  .multi-field, .group-fields {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px;
  }
  .multi-buttons {
    flex-direction: column;
    gap: 2px;
  }
  .group-actions {
    justify-content: center;
    gap: 4px;
  }
  .dropdown-input-container .dropdown-arrow-btn {
    width: 36px;
    height: 36px;
  }
  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
  .dropdown-item {
    padding: 10px 12px;
    font-size: 13px;
  }
  .dropdown-empty {
    padding: 10px 12px;
    font-size: 13px;
  }
  .grouped-field-section {
    padding: 12px;
    margin-bottom: 16px;
  }
  .grouped-field-section h3 {
    font-size: 14px;
    margin-bottom: 12px;
  }
  .row-view-table-container {
    flex-direction: column;
    gap: 12px;
  }
  .row-view-table-cell {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .form-field input,
  .form-field select {
    padding: 8px 10px;
    font-size: 14px;
  }
  .no-input-indicator {
    font-size: 9px;
    margin-left: 1px;
  }
  .multi-field, .group-fields {
    padding: 8px;
    gap: 8px;
  }
  .dropdown-input-container .dropdown-arrow-btn {
    width: 32px;
    height: 32px;
  }
  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }
  .dropdown-item {
    padding: 8px 10px;
    font-size: 12px;
  }
  .dropdown-empty {
    padding: 8px 10px;
    font-size: 12px;
  }
}

/* Column count responsive styles */
.form-grid.columns-1 .form-column {
  flex: 1;
}

.form-grid.columns-2 .form-column {
  flex: 1;
}

.form-grid.columns-3 .form-column {
  flex: 1;
}

.form-grid.columns-4 .form-column {
  flex: 1;
}

@media (min-width: 768px) {
  .form-grid {
    flex-direction: row;
  }
  .form-grid.columns-1 .form-column {
    flex: 1;
  }
  .form-grid.columns-2 .form-column {
    flex: 1;
  }
  .form-grid.columns-3 .form-column {
    flex: 1;
  }
  .form-grid.columns-4 .form-column {
    flex: 1;
  }
} 